{"UseInMemoryDatabase": true, "ConnectionStrings": {"DefaultConnection": "DataSource=:memory:"}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "JWT": {"SecretKey": "p9vB7z!Qw3rT6yU2eX8sZ4cL1nM0aJ5hR@kF#GdS$WqE^VbN*YjP", "Issuer": "DecorStore", "Audience": "DecorStoreClients", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "RequireHttpsMetadata": false, "SaveToken": true, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "EnableDebugEvents": true}, "ImageSettings": {"BasePath": "TestUploads", "MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif"]}, "Cache": {"DefaultExpirationMinutes": 5, "LongTermExpiryMinutes": 10, "ShortTermExpiryMinutes": 1, "EnableCaching": false, "EnableDistributedCache": false}, "FileStorage": {"UploadPath": "TestUploads", "ThumbnailPath": ".test-thumbnails", "MaxFileSizeMB": 10, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".webp"], "EnableImageOptimization": false, "GenerateThumbnails": false}, "Database": {"ConnectionString": "DataSource=:memory:", "MaxRetryCount": 3, "MaxRetryDelaySeconds": 5, "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "CommandTimeoutSeconds": 10}, "Api": {"RequestsPerMinute": 1000, "BurstLimit": 2000, "EnableSwagger": false, "DefaultLogLevel": "Warning", "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "RequestTimeoutSeconds": 10, "EnableCompression": false, "EnableResponseCaching": false}, "PasswordSecurity": {"MinimumLength": 6, "MaximumLength": 128, "RequireUppercase": false, "RequireLowercase": false, "RequireDigit": false, "RequireSpecialCharacter": false, "BlockCommonPasswords": false, "SaltRounds": 4, "EnableAccountLockout": false, "EnablePasswordHistory": false, "EnablePasswordExpiration": false, "EnableBreachDetection": false}}