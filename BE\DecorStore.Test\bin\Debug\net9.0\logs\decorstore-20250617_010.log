[2025-06-17 17:15:31.091 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.095 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.097 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.097 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.098 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.101 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.124 +07:00 INF] Cache warmup completed in 28.8977ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.127 +07:00 INF] HTTP Request [0d3eab7c-ac61-4705-ae59-3363168de71c]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 229 | Body: {"username":"testuser","email":"invalid-email","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":null,"dateOfBirth":null,"acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ7","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.133 +07:00 INF] Performance [0d3eab7c-ac61-4705-ae59-3363168de71c]: POST /api/Auth/register completed in 6.4382ms with status 400. {"CorrelationId":"0d3eab7c-ac61-4705-ae59-3363168de71c","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":6.4382,"StartTime":"2025-06-17T10:15:31.1272701Z","EndTime":"2025-06-17T10:15:31.1337084Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":229} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHJ7","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.133 +07:00 WRN] HTTP Response [0d3eab7c-ac61-4705-ae59-3363168de71c]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 508 | Duration: 6.9272ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Email":["The Email field is not a valid e-mail address.","Please provide a valid email address"],"Phone":["Phone number is required"],"Password":["Password cannot contain sequential characters (e.g., 123, abc)"],"DateOfBirth":["Date of birth is required","You must be at least 13 years old to register"]},"traceId":"00-d34a05de58d822476563f4c3dc07d925-affbe5630fcaa814-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ7","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.135 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.135 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
