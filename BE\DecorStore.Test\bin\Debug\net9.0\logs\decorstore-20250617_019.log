[2025-06-17 17:17:56.141 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.145 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.147 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.147 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.148 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.161 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.186 +07:00 INF] Cache warmup completed in 40.4316ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.187 +07:00 INF] HTTP Request [0a8754a7-31ca-4f31-9d0c-25a29b27e2a6]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"wrongpass","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:56.1746111+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKM","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.360 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"1165a8c4-c90d-41eb-bc1f-b4a7bade1a79","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AKM","RequestPath":"/api/Auth/register","CorrelationId":"0a8754a7-31ca-4f31-9d0c-25a29b27e2a6","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.363 +07:00 INF] Performance [0a8754a7-31ca-4f31-9d0c-25a29b27e2a6]: POST /api/Auth/register completed in 175.4323ms with status 201. {"CorrelationId":"0a8754a7-31ca-4f31-9d0c-25a29b27e2a6","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":175.4323,"StartTime":"2025-06-17T10:17:56.1876484Z","EndTime":"2025-06-17T10:17:56.3630807Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKM","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.363 +07:00 INF] HTTP Response [0a8754a7-31ca-4f31-9d0c-25a29b27e2a6]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 421 | Duration: 175.9908ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.n8uXO3ACzB0dCCt_r39TVjoK4iMKv2VagHnInL27mBQ","user":{"id":9,"username":"wrongpass","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKM","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.365 +07:00 INF] HTTP Request [b5fb9dc3-87da-45f9-9f59-e6e32beb4a3e]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"wrongpass","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:56.3639924+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKO","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.369 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-03e044ae38f245c0cda56634fa94eeba-c749336d6f3cbf46-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"1165a8c4-c90d-41eb-bc1f-b4a7bade1a79","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AKO","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.369 +07:00 INF] Performance [b5fb9dc3-87da-45f9-9f59-e6e32beb4a3e]: POST /api/Auth/register completed in 4.0404ms with status 400. {"CorrelationId":"b5fb9dc3-87da-45f9-9f59-e6e32beb4a3e","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":4.0404,"StartTime":"2025-06-17T10:17:56.3656988Z","EndTime":"2025-06-17T10:17:56.3697392Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKO","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.369 +07:00 WRN] HTTP Response [b5fb9dc3-87da-45f9-9f59-e6e32beb4a3e]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 4.4188ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-03e044ae38f245c0cda56634fa94eeba-c749336d6f3cbf46-00","timestamp":"2025-06-17T10:17:56.369Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKO","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.370 +07:00 INF] HTTP Request [d1a483dd-42f0-48b3-ba9c-7bfdb5b7a649]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 80 | Body: {"email":"<EMAIL>","password":"TestPass@word1","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKQ","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.530 +07:00 INF] Performance [d1a483dd-42f0-48b3-ba9c-7bfdb5b7a649]: POST /api/Auth/login completed in 159.4683ms with status 200. {"CorrelationId":"d1a483dd-42f0-48b3-ba9c-7bfdb5b7a649","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":159.4683,"StartTime":"2025-06-17T10:17:56.3708256Z","EndTime":"2025-06-17T10:17:56.5302941Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":80} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKQ","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.530 +07:00 INF] HTTP Response [d1a483dd-42f0-48b3-ba9c-7bfdb5b7a649]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 421 | Duration: 159.8724ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.n8uXO3ACzB0dCCt_r39TVjoK4iMKv2VagHnInL27mBQ","user":{"id":9,"username":"wrongpass","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKQ","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.532 +07:00 INF] HTTP Request [99159390-a9af-4e8a-8213-c6039e4c506c]: POST /api/Auth/change-password  | Content-Type: application/json; charset=utf-8 | Content-Length: 104 | Body: {"currentPassword":"WrongPass@word1","newPassword":"NewPass@word2","confirmNewPassword":"NewPass@word2"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKS","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.533 +07:00 INF] Performance [99159390-a9af-4e8a-8213-c6039e4c506c]: POST /api/Auth/change-password completed in 0.7266ms with status 401. {"CorrelationId":"99159390-a9af-4e8a-8213-c6039e4c506c","Method":"POST","Path":"/api/Auth/change-password","QueryString":"","StatusCode":401,"DurationMs":0.7266,"StartTime":"2025-06-17T10:17:56.5323491Z","EndTime":"2025-06-17T10:17:56.5330759Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":104} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKS","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.533 +07:00 WRN] HTTP Response [99159390-a9af-4e8a-8213-c6039e4c506c]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.1488ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKS","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.535 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.535 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
