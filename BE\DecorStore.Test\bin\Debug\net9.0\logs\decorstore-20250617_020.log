[2025-06-17 17:17:56.578 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.581 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.582 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.583 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.586 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.589 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.606 +07:00 INF] Cache warmup completed in 25.0619ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.606 +07:00 INF] HTTP Request [ea11c3fd-93bc-468c-aabf-741c0d67ea68]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"changepass","email":"<EMAIL>","password":"OldPass@word1","confirmPassword":"OldPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:56.5992333+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKU","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.770 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"8cc7a7e9-7697-4d2a-b65f-f93ed0f0f607","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AKU","RequestPath":"/api/Auth/register","CorrelationId":"ea11c3fd-93bc-468c-aabf-741c0d67ea68","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.773 +07:00 INF] Performance [ea11c3fd-93bc-468c-aabf-741c0d67ea68]: POST /api/Auth/register completed in 166.5087ms with status 201. {"CorrelationId":"ea11c3fd-93bc-468c-aabf-741c0d67ea68","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":166.5087,"StartTime":"2025-06-17T10:17:56.6067510Z","EndTime":"2025-06-17T10:17:56.7732597Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKU","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.773 +07:00 INF] HTTP Response [ea11c3fd-93bc-468c-aabf-741c0d67ea68]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 428 | Duration: 166.8359ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.TgO42CDCfPK706WL_11VyAXADcMqE_ocZeLaqGf_CXk","user":{"id":10,"username":"changepass","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKU","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.775 +07:00 INF] HTTP Request [49d75265-a946-4000-a0d7-22f280f8b5de]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"changepass","email":"<EMAIL>","password":"OldPass@word1","confirmPassword":"OldPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:56.7741612+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL0","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.796 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-77f1c2fe6716861b678aea85b978e970-4629d64538eecfb4-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"8cc7a7e9-7697-4d2a-b65f-f93ed0f0f607","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AL0","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.797 +07:00 INF] Performance [49d75265-a946-4000-a0d7-22f280f8b5de]: POST /api/Auth/register completed in 21.19ms with status 400. {"CorrelationId":"49d75265-a946-4000-a0d7-22f280f8b5de","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":21.19,"StartTime":"2025-06-17T10:17:56.7758674Z","EndTime":"2025-06-17T10:17:56.7970574Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AL0","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.797 +07:00 WRN] HTTP Response [49d75265-a946-4000-a0d7-22f280f8b5de]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 21.6072ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-77f1c2fe6716861b678aea85b978e970-4629d64538eecfb4-00","timestamp":"2025-06-17T10:17:56.796Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL0","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.797 +07:00 INF] HTTP Request [046d9f28-41fd-4358-807f-aaf92c6ca908]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 80 | Body: {"email":"<EMAIL>","password":"OldPass@word1","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL2","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.963 +07:00 INF] Performance [046d9f28-41fd-4358-807f-aaf92c6ca908]: POST /api/Auth/login completed in 164.0721ms with status 200. {"CorrelationId":"046d9f28-41fd-4358-807f-aaf92c6ca908","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":164.0721,"StartTime":"2025-06-17T10:17:56.7980608Z","EndTime":"2025-06-17T10:17:56.9621343Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":80} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AL2","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.963 +07:00 INF] HTTP Response [046d9f28-41fd-4358-807f-aaf92c6ca908]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 428 | Duration: 165.4096ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.TgO42CDCfPK706WL_11VyAXADcMqE_ocZeLaqGf_CXk","user":{"id":10,"username":"changepass","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL2","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.964 +07:00 INF] HTTP Request [f3cb0895-7b70-4a39-962c-0733cc62919b]: POST /api/Auth/change-password  | Content-Type: application/json; charset=utf-8 | Content-Length: 102 | Body: {"currentPassword":"OldPass@word1","newPassword":"NewPass@word2","confirmNewPassword":"NewPass@word2"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL4","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.965 +07:00 INF] Performance [f3cb0895-7b70-4a39-962c-0733cc62919b]: POST /api/Auth/change-password completed in 1.5731ms with status 401. {"CorrelationId":"f3cb0895-7b70-4a39-962c-0733cc62919b","Method":"POST","Path":"/api/Auth/change-password","QueryString":"","StatusCode":401,"DurationMs":1.5731,"StartTime":"2025-06-17T10:17:56.9643606Z","EndTime":"2025-06-17T10:17:56.9659339Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":102} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AL4","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.966 +07:00 WRN] HTTP Response [f3cb0895-7b70-4a39-962c-0733cc62919b]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.8384ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL4","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.967 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.967 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
