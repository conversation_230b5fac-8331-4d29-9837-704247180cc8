[2025-06-18 10:24:02.763 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.767 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.769 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.769 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.770 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.774 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.799 +07:00 INF] Cache warmup completed in 32.0496ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.800 +07:00 INF] HTTP Request [ae9f23ee-87aa-48d1-9091-287f896f65fa]: GET /api/PerformanceDashboard/cache  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8V","RequestPath":"/api/PerformanceDashboard/cache","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.801 +07:00 INF] Performance [ae9f23ee-87aa-48d1-9091-287f896f65fa]: GET /api/PerformanceDashboard/cache completed in 1.051ms with status 401. {"CorrelationId":"ae9f23ee-87aa-48d1-9091-287f896f65fa","Method":"GET","Path":"/api/PerformanceDashboard/cache","QueryString":"","StatusCode":401,"DurationMs":1.051,"StartTime":"2025-06-18T03:24:02.8005517Z","EndTime":"2025-06-18T03:24:02.8016030Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDE2U8O0Q8V","RequestPath":"/api/PerformanceDashboard/cache","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.801 +07:00 WRN] HTTP Response [ae9f23ee-87aa-48d1-9091-287f896f65fa]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.3386ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8V","RequestPath":"/api/PerformanceDashboard/cache","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.802 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.802 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
