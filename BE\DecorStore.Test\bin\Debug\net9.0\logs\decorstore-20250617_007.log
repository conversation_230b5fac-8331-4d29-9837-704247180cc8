[2025-06-17 17:15:30.574 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.577 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.578 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.579 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.580 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.581 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.609 +07:00 INF] Cache warmup completed in 32.0237ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.610 +07:00 INF] HTTP Request [444789f6-c383-4d69-abeb-b11794fd04c9]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"testuser1","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.5892316+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIJ","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.837 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"d8fabff2-d57f-4b91-b13b-4da9355f0af6","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDGVHETHIJ","RequestPath":"/api/Auth/register","CorrelationId":"444789f6-c383-4d69-abeb-b11794fd04c9","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.868 +07:00 INF] Performance [444789f6-c383-4d69-abeb-b11794fd04c9]: POST /api/Auth/register completed in 258.531ms with status 201. {"CorrelationId":"444789f6-c383-4d69-abeb-b11794fd04c9","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":258.531,"StartTime":"2025-06-17T10:15:30.6102987Z","EndTime":"2025-06-17T10:15:30.8688299Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIJ","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.869 +07:00 INF] HTTP Response [444789f6-c383-4d69-abeb-b11794fd04c9]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 421 | Duration: 258.942ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.vl-O9QYc-QbEQWQA2nzWvJJJOlLOvyTbMyrkeJpeVrw","user":{"id":6,"username":"testuser1","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIJ","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.871 +07:00 INF] HTTP Request [cd96a329-fd08-43d5-abee-9bde04747778]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"testuser2","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.5892316+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIL","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.874 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-4ca14311c848a87639a5b01f8a1923ad-bf97ab00649d19d6-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"d8fabff2-d57f-4b91-b13b-4da9355f0af6","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDGVHETHIL","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.875 +07:00 INF] Performance [cd96a329-fd08-43d5-abee-9bde04747778]: POST /api/Auth/register completed in 3.3897ms with status 400. {"CorrelationId":"cd96a329-fd08-43d5-abee-9bde04747778","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":3.3897,"StartTime":"2025-06-17T10:15:30.8716597Z","EndTime":"2025-06-17T10:15:30.8750494Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIL","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.875 +07:00 WRN] HTTP Response [cd96a329-fd08-43d5-abee-9bde04747778]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 3.9809ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-4ca14311c848a87639a5b01f8a1923ad-bf97ab00649d19d6-00","timestamp":"2025-06-17T10:15:30.874Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIL","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.876 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.876 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
