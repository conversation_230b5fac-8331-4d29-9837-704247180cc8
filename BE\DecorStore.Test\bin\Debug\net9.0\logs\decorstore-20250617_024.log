[2025-06-17 17:19:19.972 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.976 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.977 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.978 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.979 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.982 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.007 +07:00 INF] Cache warmup completed in 31.756ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.019 +07:00 INF] HTTP Request [4db21b7f-611b-4d6f-90f9-a96d57a0c902]: POST /api/Products  | Content-Type: application/json; charset=utf-8 | Content-Length: 266 | Body: {"name":"","slug":"","description":"A test product description","price":-10,"originalPrice":0,"stockQuantity":-5,"sku":"","categoryId":99999,"isFeatured":false,"isActive":true,"isDigital":false,"weight":null,"dimensions":null,"tags":null,"images":null,"imageIds":[]} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2F","RequestPath":"/api/Products","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.021 +07:00 INF] Performance [4db21b7f-611b-4d6f-90f9-a96d57a0c902]: POST /api/Products completed in 2.6161ms with status 401. {"CorrelationId":"4db21b7f-611b-4d6f-90f9-a96d57a0c902","Method":"POST","Path":"/api/Products","QueryString":"","StatusCode":401,"DurationMs":2.6161,"StartTime":"2025-06-17T10:19:20.0192650Z","EndTime":"2025-06-17T10:19:20.0218815Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":266} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH1LVSB2F","RequestPath":"/api/Products","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.022 +07:00 WRN] HTTP Response [4db21b7f-611b-4d6f-90f9-a96d57a0c902]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 4.2596ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2F","RequestPath":"/api/Products","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.023 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.023 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
