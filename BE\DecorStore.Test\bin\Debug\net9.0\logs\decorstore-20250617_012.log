[2025-06-17 17:17:54.608 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.611 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.615 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.615 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.617 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.620 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.635 +07:00 INF] Cache warmup completed in 23.938ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.656 +07:00 INF] HTTP Request [ca7e1f5c-ebb9-49ef-a86d-d017f436e277]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 278 | Body: {"username":"testuser","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"DifferentPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:54.6338193+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AJU","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.721 +07:00 INF] Performance [ca7e1f5c-ebb9-49ef-a86d-d017f436e277]: POST /api/Auth/register completed in 64.2719ms with status 400. {"CorrelationId":"ca7e1f5c-ebb9-49ef-a86d-d017f436e277","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":64.2719,"StartTime":"2025-06-17T10:17:54.6565761Z","EndTime":"2025-06-17T10:17:54.7208484Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":278} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AJU","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.721 +07:00 WRN] HTTP Response [ca7e1f5c-ebb9-49ef-a86d-d017f436e277]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 330 | Duration: 65.7861ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"ConfirmPassword":["The password and confirmation password do not match.","Password confirmation does not match the password"]},"traceId":"00-bf90456f0ebef7b7ab1fdc320c129af7-dbd3a325923d2e90-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AJU","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.723 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.723 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
