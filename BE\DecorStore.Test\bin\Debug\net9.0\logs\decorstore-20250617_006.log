[2025-06-17 17:15:30.492 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.496 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.498 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.498 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.499 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.503 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.516 +07:00 INF] HTTP Request [af9c638a-4c0b-48c0-982d-e0e1597dbb7f]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 277 | Body: {"username":"claims","email":"<EMAIL>","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.5113513+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIB","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.517 +07:00 INF] Cache warmup completed in 20.8468ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.523 +07:00 INF] Performance [af9c638a-4c0b-48c0-982d-e0e1597dbb7f]: POST /api/Auth/register completed in 6.2364ms with status 400. {"CorrelationId":"af9c638a-4c0b-48c0-982d-e0e1597dbb7f","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":6.2364,"StartTime":"2025-06-17T10:15:30.5171368Z","EndTime":"2025-06-17T10:15:30.5233733Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":277} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIB","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.523 +07:00 WRN] HTTP Response [af9c638a-4c0b-48c0-982d-e0e1597dbb7f]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 6.7495ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-d77861f69fd154d041085977ad1bf84c-24c6a3613c0fa0fa-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIB","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.528 +07:00 INF] HTTP Request [8ffd9312-60ff-4054-b8f0-6054288d9f24]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 277 | Body: {"username":"claims","email":"<EMAIL>","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.5256082+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHID","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.530 +07:00 INF] Performance [8ffd9312-60ff-4054-b8f0-6054288d9f24]: POST /api/Auth/register completed in 1.2594ms with status 400. {"CorrelationId":"8ffd9312-60ff-4054-b8f0-6054288d9f24","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":1.2594,"StartTime":"2025-06-17T10:15:30.5288782Z","EndTime":"2025-06-17T10:15:30.5301377Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":277} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHID","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.530 +07:00 WRN] HTTP Response [8ffd9312-60ff-4054-b8f0-6054288d9f24]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 2.2307ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-2f943990263b79609fe1239f551721c0-93c0cfbc0f56f69c-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHID","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.531 +07:00 INF] HTTP Request [32b35102-4ea4-4ade-a043-b83403b2a656]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 79 | Body: {"email":"<EMAIL>","password":"TestPassword123!","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIF","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.538 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-2b1a7a28c8a2e1b7d9180188c8ff4ac7-fd96649efe147510-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"fc1ff292-725b-4266-a90d-6fedbfa1c21c","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDGVHETHIF","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.539 +07:00 INF] Performance [32b35102-4ea4-4ade-a043-b83403b2a656]: POST /api/Auth/login completed in 8.0809ms with status 400. {"CorrelationId":"32b35102-4ea4-4ade-a043-b83403b2a656","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":8.0809,"StartTime":"2025-06-17T10:15:30.5316214Z","EndTime":"2025-06-17T10:15:30.5397022Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":79} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIF","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.539 +07:00 WRN] HTTP Response [32b35102-4ea4-4ade-a043-b83403b2a656]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 8.4217ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-2b1a7a28c8a2e1b7d9180188c8ff4ac7-fd96649efe147510-00","timestamp":"2025-06-17T10:15:30.539Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIF","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.540 +07:00 INF] HTTP Request [d1c84930-7140-43f5-8398-490a4eb483fd]: GET /api/Auth/check-claims  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIH","RequestPath":"/api/Auth/check-claims","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.541 +07:00 INF] Performance [d1c84930-7140-43f5-8398-490a4eb483fd]: GET /api/Auth/check-claims completed in 0.716ms with status 401. {"CorrelationId":"d1c84930-7140-43f5-8398-490a4eb483fd","Method":"GET","Path":"/api/Auth/check-claims","QueryString":"","StatusCode":401,"DurationMs":0.716,"StartTime":"2025-06-17T10:15:30.5406552Z","EndTime":"2025-06-17T10:15:30.5413716Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIH","RequestPath":"/api/Auth/check-claims","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.541 +07:00 WRN] HTTP Response [d1c84930-7140-43f5-8398-490a4eb483fd]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.0804ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIH","RequestPath":"/api/Auth/check-claims","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.543 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.543 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
