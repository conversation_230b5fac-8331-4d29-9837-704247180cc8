[2025-06-18 10:24:02.165 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.170 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.172 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.172 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.174 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.177 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.203 +07:00 INF] Cache warmup completed in 33.701ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.206 +07:00 INF] HTTP Request [6101f1ae-ab82-4c67-aab1-935d2b8e18d7]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 76 | Body: {"email":"<EMAIL>","password":"Anhvip@522","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8N","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.399 +07:00 INF] Performance [6101f1ae-ab82-4c67-aab1-935d2b8e18d7]: POST /api/Auth/login completed in 192.6892ms with status 200. {"CorrelationId":"6101f1ae-ab82-4c67-aab1-935d2b8e18d7","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":192.6892,"StartTime":"2025-06-18T03:24:02.2065525Z","EndTime":"2025-06-18T03:24:02.3992415Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":76} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDE2U8O0Q8N","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.399 +07:00 INF] HTTP Response [6101f1ae-ab82-4c67-aab1-935d2b8e18d7]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 430 | Duration: 193.2082ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.t0a4dmwuGU3YdfRCDVNHND5cn7ooKtggzqJQKHRJDv0","user":{"id":12,"username":"truongadmin","email":"<EMAIL>","role":"Admin"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8N","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.403 +07:00 INF] HTTP Request [504f9d70-f67a-4bf5-a796-7e55388ebf0b]: GET /api/PerformanceDashboard/database  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8P","RequestPath":"/api/PerformanceDashboard/database","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.405 +07:00 INF] Performance [504f9d70-f67a-4bf5-a796-7e55388ebf0b]: GET /api/PerformanceDashboard/database completed in 1.4197ms with status 401. {"CorrelationId":"504f9d70-f67a-4bf5-a796-7e55388ebf0b","Method":"GET","Path":"/api/PerformanceDashboard/database","QueryString":"","StatusCode":401,"DurationMs":1.4197,"StartTime":"2025-06-18T03:24:02.4033283Z","EndTime":"2025-06-18T03:24:02.4047488Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDE2U8O0Q8P","RequestPath":"/api/PerformanceDashboard/database","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.405 +07:00 WRN] HTTP Response [504f9d70-f67a-4bf5-a796-7e55388ebf0b]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 2.3287ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8P","RequestPath":"/api/PerformanceDashboard/database","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.407 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.407 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
