[2025-06-17 17:17:55.623 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.626 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.629 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.629 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.630 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.633 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.647 +07:00 INF] Cache warmup completed in 20.2193ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.648 +07:00 INF] HTTP Request [039529dc-d01a-40fa-b2d1-1d2a44ddc857]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 273 | Body: {"username":"claims","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:55.6420761+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKA","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.819 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"48ad02e4-87f1-4f6a-9f9e-f3fc755f4207","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AKA","RequestPath":"/api/Auth/register","CorrelationId":"039529dc-d01a-40fa-b2d1-1d2a44ddc857","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.822 +07:00 INF] Performance [039529dc-d01a-40fa-b2d1-1d2a44ddc857]: POST /api/Auth/register completed in 173.9951ms with status 201. {"CorrelationId":"039529dc-d01a-40fa-b2d1-1d2a44ddc857","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":173.9951,"StartTime":"2025-06-17T10:17:55.6488016Z","EndTime":"2025-06-17T10:17:55.8227968Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":273} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKA","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.823 +07:00 INF] HTTP Response [039529dc-d01a-40fa-b2d1-1d2a44ddc857]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 407 | Duration: 174.3418ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************.PyVm3I5W21T_i6zUryLRpyZGqtcluCFbAfohRo00Dzo","user":{"id":8,"username":"claims","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKA","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.825 +07:00 INF] HTTP Request [f29a4b7b-db5d-4836-924e-92b5cd1d96a2]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 273 | Body: {"username":"claims","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:55.8240819+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKC","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.829 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-654fddb4ae6fb0643ea48f520c96a343-bcf10c5147c8bf6d-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"48ad02e4-87f1-4f6a-9f9e-f3fc755f4207","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AKC","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.829 +07:00 INF] Performance [f29a4b7b-db5d-4836-924e-92b5cd1d96a2]: POST /api/Auth/register completed in 3.706ms with status 400. {"CorrelationId":"f29a4b7b-db5d-4836-924e-92b5cd1d96a2","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":3.706,"StartTime":"2025-06-17T10:17:55.8261384Z","EndTime":"2025-06-17T10:17:55.8298445Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":273} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKC","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.830 +07:00 WRN] HTTP Response [f29a4b7b-db5d-4836-924e-92b5cd1d96a2]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 4.1419ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-654fddb4ae6fb0643ea48f520c96a343-bcf10c5147c8bf6d-00","timestamp":"2025-06-17T10:17:55.829Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKC","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.830 +07:00 INF] HTTP Request [a86813f0-0c18-494a-9fed-edc7c4314daa]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 77 | Body: {"email":"<EMAIL>","password":"TestPass@word1","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKE","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.000 +07:00 INF] Performance [a86813f0-0c18-494a-9fed-edc7c4314daa]: POST /api/Auth/login completed in 169.4639ms with status 200. {"CorrelationId":"a86813f0-0c18-494a-9fed-edc7c4314daa","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":169.4639,"StartTime":"2025-06-17T10:17:55.8307802Z","EndTime":"2025-06-17T10:17:56.0002442Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":77} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKE","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.000 +07:00 INF] HTTP Response [a86813f0-0c18-494a-9fed-edc7c4314daa]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 407 | Duration: 170.0158ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************.PyVm3I5W21T_i6zUryLRpyZGqtcluCFbAfohRo00Dzo","user":{"id":8,"username":"claims","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKE","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.003 +07:00 INF] HTTP Request [f47e5712-2459-4612-9819-de485fdb365a]: GET /api/Auth/check-claims  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKG","RequestPath":"/api/Auth/check-claims","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.004 +07:00 INF] Performance [f47e5712-2459-4612-9819-de485fdb365a]: GET /api/Auth/check-claims completed in 0.9159ms with status 401. {"CorrelationId":"f47e5712-2459-4612-9819-de485fdb365a","Method":"GET","Path":"/api/Auth/check-claims","QueryString":"","StatusCode":401,"DurationMs":0.9159,"StartTime":"2025-06-17T10:17:56.0033698Z","EndTime":"2025-06-17T10:17:56.0042860Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKG","RequestPath":"/api/Auth/check-claims","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.004 +07:00 WRN] HTTP Response [f47e5712-2459-4612-9819-de485fdb365a]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.2541ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKG","RequestPath":"/api/Auth/check-claims","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.006 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.006 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
