using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DecorStore.API.Data;
using System.Net.Http.Json;
using System.Text.Json;
using DecorStore.API.DTOs;

namespace DecorStore.Test
{
    public class TestBase : IDisposable
    {
        protected readonly WebApplicationFactory<Program> _factory;
        protected readonly HttpClient _client;
        protected readonly IServiceScope _scope;
        protected readonly ApplicationDbContext _context;
        protected readonly JsonSerializerOptions _jsonOptions;

        public TestBase()
        {
            _factory = new WebApplicationFactory<Program>()
                .WithWebHostBuilder(builder =>
                {
                    builder.UseEnvironment("Test");
                    builder.ConfigureAppConfiguration((context, config) =>
                    {
                        // Override with test-specific settings
                        config.AddInMemoryCollection(new Dictionary<string, string?>
                        {
                            ["UseInMemoryDatabase"] = "true",
                            ["ConnectionStrings:DefaultConnection"] = "DataSource=:memory:",
                            ["JWT:RequireHttpsMetadata"] = "false",
                            ["JWT:EnableDebugEvents"] = "true",
                            ["Cache:EnableCaching"] = "false",
                            ["Cache:EnableDistributedCache"] = "false",
                            ["PasswordSecurity:RequireUppercase"] = "false",
                            ["PasswordSecurity:RequireLowercase"] = "false",
                            ["PasswordSecurity:RequireDigit"] = "false",
                            ["PasswordSecurity:RequireSpecialCharacter"] = "false",
                            ["PasswordSecurity:MinimumLength"] = "6",
                            ["PasswordSecurity:EnableAccountLockout"] = "false",
                            ["Api:EnableSwagger"] = "false"
                        });
                    });

                    builder.ConfigureServices(services =>
                    {
                        // Reduce logging noise in tests
                        services.AddLogging(builder =>
                        {
                            builder.SetMinimumLevel(LogLevel.Warning);
                        });
                    });
                });

            _client = _factory.CreateClient();
            _scope = _factory.Services.CreateScope();
            _context = _scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Ensure database is created
            _context.Database.EnsureCreated();

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };
        }

        protected async Task<string?> GetAuthTokenAsync(string email = "<EMAIL>", string password = "Anhvip@522")
        {
            // First register the admin user if not exists
            await RegisterAdminUserAsync();

            var loginDto = new LoginDTO
            {
                Email = email,
                Password = password
            };

            var response = await _client.PostAsJsonAsync("/api/Auth/login", loginDto);

            if (response.IsSuccessStatusCode)
            {
                var authResponse = await DeserializeResponseAsync<AuthResponseDTO>(response);
                return authResponse?.Token;
            }

            return null;
        }

        protected async Task<string?> GetAdminTokenAsync()
        {
            return await GetAuthTokenAsync("<EMAIL>", "Anhvip@522");
        }

        protected async Task RegisterAdminUserAsync()
        {
            // Check if admin user already exists
            var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
            if (existingUser != null)
            {
                // Update existing user to admin if not already
                if (existingUser.Role != "Admin")
                {
                    existingUser.Role = "Admin";
                    existingUser.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
                return;
            }

            // Create admin user directly in database
            var adminUser = new DecorStore.API.Models.User
            {
                Username = "truongadmin",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Anhvip@522"),
                FullName = "truong tran",
                Phone = "123456789",
                Role = "Admin", // Set as admin directly
                Status = "Active",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Users.Add(adminUser);
            await _context.SaveChangesAsync();
        }

        protected async Task<bool> RegisterTestUserAsync(string email, string password, bool isAdmin = false)
        {
            var registerDto = new RegisterDTO
            {
                Username = email.Split('@')[0],
                Email = email,
                Password = password,
                ConfirmPassword = password,
                FirstName = "Test",
                LastName = "User",
                Phone = "+1234567890",
                DateOfBirth = DateTime.Now.AddYears(-25),
                AcceptTerms = true,
                AcceptPrivacyPolicy = true
            };

            var response = await _client.PostAsJsonAsync("/api/Auth/register", registerDto);
            
            if (response.IsSuccessStatusCode && isAdmin)
            {
                // Make user admin
                var token = await GetAuthTokenAsync("<EMAIL>", "Admin123!");
                if (token != null)
                {
                    _client.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                    
                    await _client.PostAsJsonAsync("/api/Auth/make-admin", new { Email = email });
                    
                    _client.DefaultRequestHeaders.Authorization = null;
                }
            }

            return response.IsSuccessStatusCode;
        }

        protected void SetAuthHeader(string token)
        {
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        }

        protected void ClearAuthHeader()
        {
            _client.DefaultRequestHeaders.Authorization = null;
        }

        protected async Task<T?> DeserializeResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<T>(content, _jsonOptions);
        }

        protected async Task SeedTestDataAsync()
        {
            // Generate unique identifier for this test run to avoid conflicts
            var testId = Guid.NewGuid().ToString("N")[..8]; // Use first 8 characters of GUID

            // Check if data already exists to avoid duplicates
            var existingCategories = await _context.Categories.AnyAsync();
            if (existingCategories)
            {
                return; // Data already seeded for this test context
            }

            // Add test categories with unique slugs
            var categories = new[]
            {
                new DecorStore.API.Models.Category { Name = $"Living Room {testId}", Slug = $"living-room-{testId}", Description = "Living room decor" },
                new DecorStore.API.Models.Category { Name = $"Bedroom {testId}", Slug = $"bedroom-{testId}", Description = "Bedroom decor" },
                new DecorStore.API.Models.Category { Name = $"Kitchen {testId}", Slug = $"kitchen-{testId}", Description = "Kitchen decor" }
            };

            _context.Categories.AddRange(categories);
            await _context.SaveChangesAsync();

            // Add test products with unique names
            var products = new[]
            {
                new DecorStore.API.Models.Product
                {
                    Name = $"Test Sofa {testId}",
                    Description = "A comfortable test sofa",
                    Price = 999.99m,
                    CategoryId = categories[0].Id,
                    IsActive = true,
                    IsFeatured = true,
                    StockQuantity = 10
                },
                new DecorStore.API.Models.Product
                {
                    Name = $"Test Bed {testId}",
                    Description = "A comfortable test bed",
                    Price = 1299.99m,
                    CategoryId = categories[1].Id,
                    IsActive = true,
                    IsFeatured = false,
                    StockQuantity = 5
                },
                new DecorStore.API.Models.Product
                {
                    Name = $"Test Table {testId}",
                    Description = "A sturdy test table",
                    Price = 599.99m,
                    CategoryId = categories[2].Id,
                    IsActive = true,
                    IsFeatured = true,
                    StockQuantity = 8
                }
            };

            _context.Products.AddRange(products);
            await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context?.Dispose();
            _scope?.Dispose();
            _client?.Dispose();
            _factory?.Dispose();
        }
    }
}
