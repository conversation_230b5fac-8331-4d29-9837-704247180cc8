using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DecorStore.API.Data;
using System.Net.Http.Json;
using System.Text.Json;
using DecorStore.API.DTOs;

namespace DecorStore.Test
{
    public class TestBase : IDisposable
    {
        protected readonly WebApplicationFactory<Program> _factory;
        protected readonly HttpClient _client;
        protected readonly IServiceScope _scope;
        protected readonly ApplicationDbContext _context;
        protected readonly JsonSerializerOptions _jsonOptions;

        public TestBase()
        {
            _factory = new WebApplicationFactory<Program>()
                .WithWebHostBuilder(builder =>
                {
                    builder.UseEnvironment("Test");
                    builder.ConfigureAppConfiguration((context, config) =>
                    {
                        // Override with test-specific settings
                        config.AddInMemoryCollection(new Dictionary<string, string?>
                        {
                            ["UseInMemoryDatabase"] = "true",
                            ["ConnectionStrings:DefaultConnection"] = "DataSource=:memory:",
                            ["JWT:RequireHttpsMetadata"] = "false",
                            ["JWT:EnableDebugEvents"] = "true",
                            ["Cache:EnableCaching"] = "false",
                            ["Cache:EnableDistributedCache"] = "false",
                            ["PasswordSecurity:RequireUppercase"] = "false",
                            ["PasswordSecurity:RequireLowercase"] = "false",
                            ["PasswordSecurity:RequireDigit"] = "false",
                            ["PasswordSecurity:RequireSpecialCharacter"] = "false",
                            ["PasswordSecurity:MinimumLength"] = "6",
                            ["PasswordSecurity:EnableAccountLockout"] = "false",
                            ["Api:EnableSwagger"] = "false"
                        });
                    });

                    builder.ConfigureServices(services =>
                    {
                        // Reduce logging noise in tests
                        services.AddLogging(builder =>
                        {
                            builder.SetMinimumLevel(LogLevel.Warning);
                        });
                    });
                });

            _client = _factory.CreateClient();
            _scope = _factory.Services.CreateScope();
            _context = _scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Ensure database is created
            _context.Database.EnsureCreated();

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };
        }

        protected async Task<string?> GetAuthTokenAsync(string email = "<EMAIL>", string password = "Admin123!")
        {
            // First register an admin user if not exists
            await RegisterTestUserAsync(email, password, isAdmin: true);

            var loginDto = new LoginDTO
            {
                Email = email,
                Password = password
            };

            var response = await _client.PostAsJsonAsync("/api/Auth/login", loginDto);
            
            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponseDTO>();
                return authResponse?.Token;
            }

            return null;
        }

        protected async Task<bool> RegisterTestUserAsync(string email, string password, bool isAdmin = false)
        {
            var registerDto = new RegisterDTO
            {
                Username = email.Split('@')[0],
                Email = email,
                Password = password,
                ConfirmPassword = password,
                FirstName = "Test",
                LastName = "User",
                Phone = "+1234567890",
                DateOfBirth = DateTime.Now.AddYears(-25),
                AcceptTerms = true,
                AcceptPrivacyPolicy = true
            };

            var response = await _client.PostAsJsonAsync("/api/Auth/register", registerDto);
            
            if (response.IsSuccessStatusCode && isAdmin)
            {
                // Make user admin
                var token = await GetAuthTokenAsync("<EMAIL>", "Admin123!");
                if (token != null)
                {
                    _client.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                    
                    await _client.PostAsJsonAsync("/api/Auth/make-admin", new { Email = email });
                    
                    _client.DefaultRequestHeaders.Authorization = null;
                }
            }

            return response.IsSuccessStatusCode;
        }

        protected void SetAuthHeader(string token)
        {
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        }

        protected void ClearAuthHeader()
        {
            _client.DefaultRequestHeaders.Authorization = null;
        }

        protected async Task<T?> DeserializeResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<T>(content, _jsonOptions);
        }

        protected async Task SeedTestDataAsync()
        {
            // Add test categories
            var categories = new[]
            {
                new DecorStore.API.Models.Category { Name = "Living Room", Slug = "living-room", Description = "Living room decor" },
                new DecorStore.API.Models.Category { Name = "Bedroom", Slug = "bedroom", Description = "Bedroom decor" },
                new DecorStore.API.Models.Category { Name = "Kitchen", Slug = "kitchen", Description = "Kitchen decor" }
            };

            _context.Categories.AddRange(categories);
            await _context.SaveChangesAsync();

            // Add test products
            var products = new[]
            {
                new DecorStore.API.Models.Product 
                { 
                    Name = "Test Sofa", 
                    Description = "A comfortable test sofa", 
                    Price = 999.99m, 
                    CategoryId = categories[0].Id,
                    IsActive = true,
                    IsFeatured = true,
                    StockQuantity = 10
                },
                new DecorStore.API.Models.Product 
                { 
                    Name = "Test Bed", 
                    Description = "A comfortable test bed", 
                    Price = 1299.99m, 
                    CategoryId = categories[1].Id,
                    IsActive = true,
                    IsFeatured = false,
                    StockQuantity = 5
                }
            };

            _context.Products.AddRange(products);
            await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context?.Dispose();
            _scope?.Dispose();
            _client?.Dispose();
            _factory?.Dispose();
        }
    }
}
