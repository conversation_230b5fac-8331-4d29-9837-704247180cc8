[2025-06-17 17:15:30.989 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.992 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.995 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.995 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.997 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.999 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.014 +07:00 INF] HTTP Request [d9749c3b-7e1b-4f17-86d7-e1dedc3c2af1]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 283 | Body: {"username":"changepass","email":"<EMAIL>","password":"OldPassword123!","confirmPassword":"OldPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:31.0080812+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIV","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.016 +07:00 INF] Cache warmup completed in 23.6845ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.019 +07:00 INF] Performance [d9749c3b-7e1b-4f17-86d7-e1dedc3c2af1]: POST /api/Auth/register completed in 4.452ms with status 400. {"CorrelationId":"d9749c3b-7e1b-4f17-86d7-e1dedc3c2af1","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":4.452,"StartTime":"2025-06-17T10:15:31.0146348Z","EndTime":"2025-06-17T10:15:31.0190869Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":283} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIV","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.019 +07:00 WRN] HTTP Response [d9749c3b-7e1b-4f17-86d7-e1dedc3c2af1]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 4.8751ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-d732bc9a4e6055632ff88f09c255302a-e787018582ea8e00-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIV","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.022 +07:00 INF] HTTP Request [e2518a09-0dae-4d5e-9f2c-542063d3be44]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 283 | Body: {"username":"changepass","email":"<EMAIL>","password":"OldPassword123!","confirmPassword":"OldPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:31.0199316+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ1","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.024 +07:00 INF] Performance [e2518a09-0dae-4d5e-9f2c-542063d3be44]: POST /api/Auth/register completed in 1.8036ms with status 400. {"CorrelationId":"e2518a09-0dae-4d5e-9f2c-542063d3be44","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":1.8036,"StartTime":"2025-06-17T10:15:31.0222030Z","EndTime":"2025-06-17T10:15:31.0240067Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":283} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHJ1","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.024 +07:00 WRN] HTTP Response [e2518a09-0dae-4d5e-9f2c-542063d3be44]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 2.3991ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-e75ad3d35def9b060d491a0f49dff260-16c538f54a544531-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ1","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.025 +07:00 INF] HTTP Request [01ed6315-9b72-417e-99c9-2a45d2e9ba91]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 82 | Body: {"email":"<EMAIL>","password":"OldPassword123!","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ3","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.056 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-********************************-db63b3e6a1a4f6c7-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"746f8818-d5dc-4436-b7fe-67277122abbb","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDGVHETHJ3","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.057 +07:00 INF] Performance [01ed6315-9b72-417e-99c9-2a45d2e9ba91]: POST /api/Auth/login completed in 31.795ms with status 400. {"CorrelationId":"01ed6315-9b72-417e-99c9-2a45d2e9ba91","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":31.795,"StartTime":"2025-06-17T10:15:31.0252123Z","EndTime":"2025-06-17T10:15:31.0570074Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":82} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHJ3","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.057 +07:00 WRN] HTTP Response [01ed6315-9b72-417e-99c9-2a45d2e9ba91]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 32.2316ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-********************************-db63b3e6a1a4f6c7-00","timestamp":"2025-06-17T10:15:31.056Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ3","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.058 +07:00 INF] HTTP Request [37851569-1bb7-4176-8489-700e61198945]: POST /api/Auth/change-password  | Content-Type: application/json; charset=utf-8 | Content-Length: 108 | Body: {"currentPassword":"OldPassword123!","newPassword":"NewPassword123!","confirmNewPassword":"NewPassword123!"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ5","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.059 +07:00 INF] Performance [37851569-1bb7-4176-8489-700e61198945]: POST /api/Auth/change-password completed in 0.7841ms with status 401. {"CorrelationId":"37851569-1bb7-4176-8489-700e61198945","Method":"POST","Path":"/api/Auth/change-password","QueryString":"","StatusCode":401,"DurationMs":0.7841,"StartTime":"2025-06-17T10:15:31.0582654Z","EndTime":"2025-06-17T10:15:31.0590504Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":108} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHJ5","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.059 +07:00 WRN] HTTP Response [37851569-1bb7-4176-8489-700e61198945]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.6084ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ5","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.061 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.061 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
