{"UseInMemoryDatabase": false, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=DecorStoreDb;Trusted_Connection=True;TrustServerCertificate=True"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JWT": {"SecretKey": "p9vB7z!Qw3rT6yU2eX8sZ4cL1nM0aJ5hR@kF#GdS$WqE^VbN*YjP", "Issuer": "DecorStore", "Audience": "DecorStoreClients", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "RequireHttpsMetadata": true, "SaveToken": true, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "EnableDebugEvents": false}, "ImageSettings": {"BasePath": "Uploads", "MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif"]}, "Cache": {"DefaultExpirationMinutes": 60, "LongTermExpiryMinutes": 1440, "ShortTermExpiryMinutes": 15, "EnableCaching": true, "EnableDistributedCache": false, "CacheKeyPrefix": "DecorStore", "MaxCacheSizeMB": 100, "DefaultSizeLimit": 1000000, "SlidingExpirationMinutes": 30, "CompressLargeValues": true, "CompressionThresholdBytes": 10240, "RedisDatabase": 0, "RedisTimeoutMs": 5000, "EnableCacheWarming": true, "CacheWarmupKeys": ["categories:all", "products:featured", "dashboard:stats"]}, "FileStorage": {"UploadPath": "Uploads", "ThumbnailPath": ".thumbnails", "MaxFileSizeMB": 10, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".webp"], "ThumbnailWidth": 200, "ThumbnailHeight": 200, "ImageQuality": 85, "EnableImageOptimization": true, "GenerateThumbnails": true, "MaxFilesPerDirectory": 1000, "UseSubdirectories": true, "BaseUrl": "/uploads", "ThumbnailUrl": "/.thumbnails"}, "Database": {"ConnectionString": "Server=(localdb)\\MSSQLLocalDB;Database=DecorStoreDb;Trusted_Connection=True;TrustServerCertificate=True", "MaxRetryCount": 5, "MaxRetryDelaySeconds": 30, "MigrationHistoryTable": "__EFMigrationsHistory", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "CommandTimeoutSeconds": 30}, "Api": {"DefaultVersion": "v1", "SupportedVersions": ["v1"], "RequestsPerMinute": 100, "BurstLimit": 200, "AllowedOrigins": ["https://localhost", "http://localhost"], "AllowedHeaders": ["Content-Type", "Authorization", "X-Correlation-ID"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowCredentials": true, "EnableSwagger": true, "SwaggerEndpoint": "/swagger/v1/swagger.json", "SwaggerTitle": "DecorStore API", "DefaultLogLevel": "Information", "MicrosoftLogLevel": "Warning", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "RequestTimeoutSeconds": 30, "MaxRequestBodySizeMB": 100, "EnableCompression": true, "EnableResponseCaching": true, "DefaultCacheDurationSeconds": 300}, "PasswordSecurity": {"MinimumLength": 8, "MaximumLength": 128, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialCharacter": true, "BlockCommonPasswords": true, "BlockSequentialCharacters": true, "BlockRepeatedCharacters": true, "SaltRounds": 12, "EnableAccountLockout": true, "MaxFailedAccessAttempts": 5, "LockoutDurationMinutes": 30, "EnablePasswordHistory": true, "PasswordHistoryCount": 5, "EnablePasswordExpiration": true, "PasswordExpirationDays": 90, "EnableBreachDetection": true}, "JwtSecurity": {"AccessTokenExpiryMinutes": 15, "RefreshTokenExpiryDays": 7, "MaxRefreshTokenFamilySize": 5, "TokenBindingDurationMinutes": 30, "TokenReplayWindowMinutes": 5, "EnableTokenEncryption": false, "EncryptionKey": ""}, "DataEncryption": {"MasterKey": "YourSuperSecretEncryptionKey12345678901234567890", "Salt": "YourSaltValue1234567890", "KeyDerivationIterations": 50000, "CurrentKeyVersion": 1, "KeyRotationDays": 365}, "ApiKey": {"DefaultExpirationDays": 365, "DefaultRateLimitPerHour": 1000, "DefaultRateLimitPerDay": 10000, "CleanupExpiredKeysAfterDays": 30}, "ApiKeyMiddleware": {"MaxSuspiciousRequestsPerHour": 100, "ExemptPaths": ["/health", "/swagger"]}}