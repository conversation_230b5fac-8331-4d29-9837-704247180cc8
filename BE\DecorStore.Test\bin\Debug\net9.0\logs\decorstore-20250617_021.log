[2025-06-17 17:17:57.008 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.012 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.014 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.014 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.015 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.019 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.044 +07:00 INF] Cache warmup completed in 32.529ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.045 +07:00 INF] HTTP Request [c4dd9880-fc74-4940-b79b-331cbb9d7183]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 229 | Body: {"username":"testuser","email":"invalid-email","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":null,"dateOfBirth":null,"acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL6","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.052 +07:00 INF] Performance [c4dd9880-fc74-4940-b79b-331cbb9d7183]: POST /api/Auth/register completed in 6.949ms with status 400. {"CorrelationId":"c4dd9880-fc74-4940-b79b-331cbb9d7183","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":6.949,"StartTime":"2025-06-17T10:17:57.0453848Z","EndTime":"2025-06-17T10:17:57.0523338Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":229} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AL6","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.052 +07:00 WRN] HTTP Response [c4dd9880-fc74-4940-b79b-331cbb9d7183]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 508 | Duration: 7.3721ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Email":["The Email field is not a valid e-mail address.","Please provide a valid email address"],"Phone":["Phone number is required"],"Password":["Password cannot contain sequential characters (e.g., 123, abc)"],"DateOfBirth":["Date of birth is required","You must be at least 13 years old to register"]},"traceId":"00-8be051e2e0169c1c8169bd3763421a5a-c8a92eecd6c850f8-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL6","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.053 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.053 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
