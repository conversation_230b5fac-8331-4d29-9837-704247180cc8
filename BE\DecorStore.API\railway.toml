[build]
builder = "nixpacks"
buildCommand = "dotnet restore && dotnet publish -c Release -o out"

[deploy]
startCommand = "cd out && dotnet DecorStore.API.dll"
healthcheckPath = "/api/products"
healthcheckTimeout = 300
restartPolicyType = "on-failure"
restartPolicyMaxRetries = 5

[env]
ASPNETCORE_ENVIRONMENT = "Production"
DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "true"
ASPNETCORE_URLS = "http://0.0.0.0:${PORT:-8080}"

# Variables for Railway Database
# DATABASE_URL will be automatically provided by Railway when linking with PostgreSQL