{"format": 1, "restore": {"D:\\Personal Projects\\Decor\\BE\\DecorStore.Test\\DecorStore.Test.csproj": {}}, "projects": {"D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\DecorStore.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\DecorStore.API.csproj", "projectName": "DecorStore.API", "projectPath": "D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\DecorStore.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNetCore.HealthChecks.Redis": {"target": "Package", "version": "[8.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "EPPlus": {"target": "Package", "version": "[7.4.2, )"}, "FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.11.0, )"}, "HtmlSanitizer": {"target": "Package", "version": "[8.1.870, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.AspNetCore.DataProtection.Extensions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.3, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.9, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.3.1, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Personal Projects\\Decor\\BE\\DecorStore.Test\\DecorStore.Test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Personal Projects\\Decor\\BE\\DecorStore.Test\\DecorStore.Test.csproj", "projectName": "DecorStore.Test", "projectPath": "D:\\Personal Projects\\Decor\\BE\\DecorStore.Test\\DecorStore.Test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Personal Projects\\Decor\\BE\\DecorStore.Test\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\DecorStore.API.csproj": {"projectPath": "D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\DecorStore.API.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.2, )"}, "Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[9.0.0, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}