[2025-06-17 17:17:57.092 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.096 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.097 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.097 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.099 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.101 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.117 +07:00 INF] HTTP Request [3256fd68-d9d2-4be8-9043-8d2a311b593e]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 283 | Body: {"username":"currentuser","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:57.1109971+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL8","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.119 +07:00 INF] Cache warmup completed in 23.1143ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.284 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"57629758-d2e9-4195-8d24-3b5d587d5f81","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AL8","RequestPath":"/api/Auth/register","CorrelationId":"3256fd68-d9d2-4be8-9043-8d2a311b593e","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.287 +07:00 INF] Performance [3256fd68-d9d2-4be8-9043-8d2a311b593e]: POST /api/Auth/register completed in 170.4595ms with status 201. {"CorrelationId":"3256fd68-d9d2-4be8-9043-8d2a311b593e","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":170.4595,"StartTime":"2025-06-17T10:17:57.1172915Z","EndTime":"2025-06-17T10:17:57.2877510Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":283} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AL8","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.287 +07:00 INF] HTTP Response [3256fd68-d9d2-4be8-9043-8d2a311b593e]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 433 | Duration: 170.8705ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************.xNjBweQQJmsygBX0sv1igq5MZ9luSdg_bYYbk9TNapo","user":{"id":11,"username":"currentuser","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AL8","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.290 +07:00 INF] HTTP Request [74eb06d0-5bd8-406d-aa49-e1462dd96bab]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 282 | Body: {"username":"currentuser","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:57.288539+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5ALA","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.292 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-ff2badff7abf72d74765878ff7634321-d90e67ffd916e628-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"57629758-d2e9-4195-8d24-3b5d587d5f81","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5ALA","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.293 +07:00 INF] Performance [74eb06d0-5bd8-406d-aa49-e1462dd96bab]: POST /api/Auth/register completed in 3.2257ms with status 400. {"CorrelationId":"74eb06d0-5bd8-406d-aa49-e1462dd96bab","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":3.2257,"StartTime":"2025-06-17T10:17:57.2901971Z","EndTime":"2025-06-17T10:17:57.2934228Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":282} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5ALA","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.293 +07:00 WRN] HTTP Response [74eb06d0-5bd8-406d-aa49-e1462dd96bab]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 3.5082ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-ff2badff7abf72d74765878ff7634321-d90e67ffd916e628-00","timestamp":"2025-06-17T10:17:57.292Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5ALA","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.294 +07:00 INF] HTTP Request [0af11de2-5f80-45a6-85ea-4378ae21baf8]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 82 | Body: {"email":"<EMAIL>","password":"TestPass@word1","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5ALC","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.455 +07:00 INF] Performance [0af11de2-5f80-45a6-85ea-4378ae21baf8]: POST /api/Auth/login completed in 161.5397ms with status 200. {"CorrelationId":"0af11de2-5f80-45a6-85ea-4378ae21baf8","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":161.5397,"StartTime":"2025-06-17T10:17:57.2943880Z","EndTime":"2025-06-17T10:17:57.4559279Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":82} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5ALC","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.456 +07:00 INF] HTTP Response [0af11de2-5f80-45a6-85ea-4378ae21baf8]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 433 | Duration: 161.9131ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************.xNjBweQQJmsygBX0sv1igq5MZ9luSdg_bYYbk9TNapo","user":{"id":11,"username":"currentuser","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5ALC","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.457 +07:00 INF] HTTP Request [eff2e4c6-c62f-45e8-bb8c-88b4901d2954]: GET /api/Auth/user  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5ALE","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.457 +07:00 INF] Performance [eff2e4c6-c62f-45e8-bb8c-88b4901d2954]: GET /api/Auth/user completed in 0.8055ms with status 401. {"CorrelationId":"eff2e4c6-c62f-45e8-bb8c-88b4901d2954","Method":"GET","Path":"/api/Auth/user","QueryString":"","StatusCode":401,"DurationMs":0.8055,"StartTime":"2025-06-17T10:17:57.4571371Z","EndTime":"2025-06-17T10:17:57.4579428Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5ALE","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.458 +07:00 WRN] HTTP Response [eff2e4c6-c62f-45e8-bb8c-88b4901d2954]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.0457ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5ALE","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.459 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:57.459 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:18.625 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:18.647 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.019 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.020 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.021 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.021 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.021 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.021 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.021 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.412 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.413 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.423 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.554 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.596 +07:00 INF] HTTP Request [4567586a-920c-4c47-944c-cea1eded86d3]: GET /api/Products/99999  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2B","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.751 +07:00 INF] Cache warmup completed in 1103.7309ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.757 +07:00 INF] Performance [4567586a-920c-4c47-944c-cea1eded86d3]: GET /api/Products/99999 completed in 157.3351ms with status 200. {"CorrelationId":"4567586a-920c-4c47-944c-cea1eded86d3","Method":"GET","Path":"/api/Products/99999","QueryString":"","StatusCode":200,"DurationMs":157.3351,"StartTime":"2025-06-17T10:19:19.5982783Z","EndTime":"2025-06-17T10:19:19.7556140Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH1LVSB2B","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.759 +07:00 INF] HTTP Response [4567586a-920c-4c47-944c-cea1eded86d3]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 163.6097ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2B","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.766 +07:00 ERR] Unhandled exception occurred [3cbdb504a1cc49cdb01ca27d13612222]: AsyncValidatorInvokedSynchronouslyException - Validator "ProductAvailabilityValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.. Context: {"CorrelationId":"3cbdb504a1cc49cdb01ca27d13612222","ExceptionType":"AsyncValidatorInvokedSynchronouslyException","Message":"Validator \"ProductAvailabilityValidator\" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.","StackTrace":"   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207\r\n   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153\r\n   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)\r\n   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\PerformanceLoggingMiddleware.cs:line 31\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 38\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 51\r\n   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\CorrelationIdMiddleware.cs:line 29\r\n   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)\r\n   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ApiKeyRateLimitingMiddleware.cs:line 101\r\n   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Extensions\\SecurityExtensions.cs:line 161\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 99\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"Anonymous","UserAgent":"Unknown","IpAddress":"Unknown","RequestPath":"/api/Products/99999","RequestMethod":"GET","QueryString":"","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDDH1LVSB2B","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
FluentValidation.AsyncValidatorInvokedSynchronouslyException: Validator "ProductAvailabilityValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.
   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207
   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153
   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45
   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63
   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 99
   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 17:19:19.818 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.818 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
