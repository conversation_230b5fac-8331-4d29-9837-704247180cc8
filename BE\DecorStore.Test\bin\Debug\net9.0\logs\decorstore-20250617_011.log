[2025-06-17 17:15:31.174 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.177 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.178 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.179 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.180 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.182 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.198 +07:00 INF] HTTP Request [eb9b6aee-df7c-447e-9a4a-5ffd0c5f3025]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 287 | Body: {"username":"currentuser","email":"<EMAIL>","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:31.1913625+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ9","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.198 +07:00 INF] Cache warmup completed in 20.8374ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.203 +07:00 INF] Performance [eb9b6aee-df7c-447e-9a4a-5ffd0c5f3025]: POST /api/Auth/register completed in 4.9375ms with status 400. {"CorrelationId":"eb9b6aee-df7c-447e-9a4a-5ffd0c5f3025","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":4.9375,"StartTime":"2025-06-17T10:15:31.1982690Z","EndTime":"2025-06-17T10:15:31.2032064Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":287} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHJ9","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.203 +07:00 WRN] HTTP Response [eb9b6aee-df7c-447e-9a4a-5ffd0c5f3025]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 5.3085ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-a8a88d449e4adebf377641442453678e-8c986c795ca49df9-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJ9","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.205 +07:00 INF] HTTP Request [1bbab83d-6017-43c4-9f3e-92d79839a962]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 287 | Body: {"username":"currentuser","email":"<EMAIL>","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:31.2039999+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJB","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.206 +07:00 INF] Performance [1bbab83d-6017-43c4-9f3e-92d79839a962]: POST /api/Auth/register completed in 0.8609ms with status 400. {"CorrelationId":"1bbab83d-6017-43c4-9f3e-92d79839a962","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":0.8609,"StartTime":"2025-06-17T10:15:31.2056127Z","EndTime":"2025-06-17T10:15:31.2064736Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":287} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHJB","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.206 +07:00 WRN] HTTP Response [1bbab83d-6017-43c4-9f3e-92d79839a962]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 1.1971ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-c1dfa8d0bd03c97c0a3e0cabd7089fd7-d833d2b678799385-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJB","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.207 +07:00 INF] HTTP Request [6de797cd-908e-4896-a47f-8dad60ffc08e]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 84 | Body: {"email":"<EMAIL>","password":"TestPassword123!","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJD","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.212 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-744c0ae110b7fc105548ce6fce42233b-14d632445059ce1a-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"03284120-4816-403a-b2d2-f4e2ae49bcf0","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDGVHETHJD","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.213 +07:00 INF] Performance [6de797cd-908e-4896-a47f-8dad60ffc08e]: POST /api/Auth/login completed in 6.3202ms with status 400. {"CorrelationId":"6de797cd-908e-4896-a47f-8dad60ffc08e","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":6.3202,"StartTime":"2025-06-17T10:15:31.2071126Z","EndTime":"2025-06-17T10:15:31.2134328Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":84} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHJD","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.213 +07:00 WRN] HTTP Response [6de797cd-908e-4896-a47f-8dad60ffc08e]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 6.5788ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-744c0ae110b7fc105548ce6fce42233b-14d632445059ce1a-00","timestamp":"2025-06-17T10:15:31.213Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHJD","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.215 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:31.215 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.427 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.448 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.821 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.822 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.822 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.822 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.822 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.822 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:53.822 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.189 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.190 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.199 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.339 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.365 +07:00 INF] HTTP Request [7193b040-439f-41b0-bc61-6624cae0ba3a]: GET /api/Auth/user  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AJS","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.395 +07:00 INF] Performance [7193b040-439f-41b0-bc61-6624cae0ba3a]: GET /api/Auth/user completed in 26.3087ms with status 401. {"CorrelationId":"7193b040-439f-41b0-bc61-6624cae0ba3a","Method":"GET","Path":"/api/Auth/user","QueryString":"","StatusCode":401,"DurationMs":26.3087,"StartTime":"2025-06-17T10:17:54.3667024Z","EndTime":"2025-06-17T10:17:54.3930111Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AJS","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.397 +07:00 WRN] HTTP Response [7193b040-439f-41b0-bc61-6624cae0ba3a]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 33.5814ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AJS","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.408 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.408 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.541 +07:00 INF] Cache warmup completed in 1092.473ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
