using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using DecorStore.API.Interfaces.Services;
using DecorStore.API.Services;
using System.Diagnostics;

namespace DecorStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")] // Only admins can access performance metrics
    public class PerformanceController : ControllerBase
    {
        private readonly ICacheService _cacheService;
        private readonly IDistributedCacheService _distributedCacheService;
        private readonly ILogger<PerformanceController> _logger;

        public PerformanceController(
            ICacheService cacheService,
            IDistributedCacheService distributedCacheService,
            ILogger<PerformanceController> logger)
        {
            _cacheService = cacheService;
            _distributedCacheService = distributedCacheService;
            _logger = logger;
        }

        /// <summary>
        /// Get cache performance statistics
        /// </summary>
        [HttpGet("cache/statistics")]
        public ActionResult<CacheStatistics> GetCacheStatistics()
        {
            try
            {
                var statistics = _cacheService.GetStatistics();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get cache key information
        /// </summary>
        [HttpGet("cache/keys")]
        public ActionResult<IEnumerable<CacheKeyInfo>> GetCacheKeys()
        {
            try
            {
                var keyInfos = _cacheService.GetKeyInfos();
                return Ok(keyInfos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache keys");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get system performance metrics
        /// </summary>
        [HttpGet("system")]
        public ActionResult<object> GetSystemMetrics()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var workingSet = GC.GetTotalMemory(false);
                var gcCollections = new[]
                {
                    GC.CollectionCount(0),
                    GC.CollectionCount(1),
                    GC.CollectionCount(2)
                };

                var metrics = new
                {
                    Memory = new
                    {
                        WorkingSetMB = workingSet / (1024 * 1024),
                        PrivateMemoryMB = process.PrivateMemorySize64 / (1024 * 1024),
                        VirtualMemoryMB = process.VirtualMemorySize64 / (1024 * 1024)
                    },
                    CPU = new
                    {
                        TotalProcessorTime = process.TotalProcessorTime.TotalMilliseconds,
                        UserProcessorTime = process.UserProcessorTime.TotalMilliseconds,
                        ProcessorCount = Environment.ProcessorCount
                    },
                    GarbageCollection = new
                    {
                        Gen0Collections = gcCollections[0],
                        Gen1Collections = gcCollections[1],
                        Gen2Collections = gcCollections[2],
                        TotalMemory = workingSet
                    },
                    Threading = new
                    {
                        ThreadCount = process.Threads.Count,
                        ThreadPoolWorkerThreads = ThreadPool.ThreadCount,
                        ThreadPoolCompletionPortThreads = ThreadPool.CompletedWorkItemCount
                    },
                    Uptime = DateTime.UtcNow - process.StartTime.ToUniversalTime(),
                    CollectedAt = DateTime.UtcNow
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting system metrics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get Redis connection status and metrics
        /// </summary>
        [HttpGet("redis")]
        public async Task<ActionResult<object>> GetRedisMetrics()
        {
            try
            {
                var isConnected = await _distributedCacheService.IsConnectedAsync();
                var keysCount = await _distributedCacheService.GetKeysCountAsync();
                var sampleKeys = await _distributedCacheService.GetKeysAsync("*");

                var metrics = new
                {
                    IsConnected = isConnected,
                    KeysCount = keysCount,
                    SampleKeys = sampleKeys.Take(10), // Show first 10 keys as sample
                    CollectedAt = DateTime.UtcNow
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Redis metrics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Clear all cache (memory and distributed)
        /// </summary>
        [HttpPost("cache/clear")]
        public async Task<ActionResult> ClearCache()
        {
            try
            {
                _cacheService.Clear();
                await _distributedCacheService.ClearAsync();
                
                _logger.LogInformation("Cache cleared by admin user");
                return Ok(new { Message = "Cache cleared successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Clear cache by prefix
        /// </summary>
        [HttpPost("cache/clear/{prefix}")]
        public async Task<ActionResult> ClearCacheByPrefix(string prefix)
        {
            try
            {
                _cacheService.RemoveByPrefix(prefix);
                await _distributedCacheService.RemoveByPatternAsync($"{prefix}*");
                
                _logger.LogInformation("Cache cleared for prefix {Prefix} by admin user", prefix);
                return Ok(new { Message = $"Cache cleared for prefix '{prefix}' successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache by prefix {Prefix}", prefix);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Warm up cache manually
        /// </summary>
        [HttpPost("cache/warmup")]
        public ActionResult WarmUpCache()
        {
            try
            {
                _cacheService.WarmUp();
                
                _logger.LogInformation("Cache warmup triggered by admin user");
                return Ok(new { Message = "Cache warmup initiated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error warming up cache");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Force garbage collection
        /// </summary>
        [HttpPost("system/gc")]
        public ActionResult ForceGarbageCollection()
        {
            try
            {
                var beforeMemory = GC.GetTotalMemory(false);
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                var afterMemory = GC.GetTotalMemory(true);

                var result = new
                {
                    BeforeMemoryMB = beforeMemory / (1024 * 1024),
                    AfterMemoryMB = afterMemory / (1024 * 1024),
                    FreedMemoryMB = (beforeMemory - afterMemory) / (1024 * 1024),
                    CollectedAt = DateTime.UtcNow
                };

                _logger.LogInformation("Garbage collection forced by admin user, freed {FreedMB}MB", result.FreedMemoryMB);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error forcing garbage collection");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get performance dashboard data
        /// </summary>
        [HttpGet("dashboard")]
        public async Task<ActionResult<object>> GetPerformanceDashboard()
        {            try
            {
                var cacheStats = _cacheService.GetStatistics();
                dynamic systemMetrics = GetSystemMetricsInternal();
                var redisConnected = await _distributedCacheService.IsConnectedAsync();
                var redisKeysCount = await _distributedCacheService.GetKeysCountAsync();

                var dashboard = new
                {
                    Cache = new
                    {
                        HitRatio = cacheStats.HitRatio,
                        TotalRequests = cacheStats.TotalRequests,
                        TotalKeys = cacheStats.TotalKeys,
                        MemorySizeMB = cacheStats.MemoryCacheSize / (1024 * 1024)
                    },
                    System = new
                    {
                        MemoryUsageMB = systemMetrics.MemoryUsageMB,
                        CpuTime = systemMetrics.CpuTime,
                        ThreadCount = systemMetrics.ThreadCount,
                        UptimeHours = systemMetrics.UptimeHours
                    },
                    Redis = new
                    {
                        IsConnected = redisConnected,
                        KeysCount = redisKeysCount
                    },
                    LastUpdated = DateTime.UtcNow
                };

                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting performance dashboard");
                return StatusCode(500, "Internal server error");
            }
        }

        private object GetSystemMetricsInternal()
        {
            var process = Process.GetCurrentProcess();
            var workingSet = GC.GetTotalMemory(false);

            return new
            {
                MemoryUsageMB = workingSet / (1024 * 1024),
                CpuTime = process.TotalProcessorTime.TotalMilliseconds,
                ThreadCount = process.Threads.Count,
                UptimeHours = (DateTime.UtcNow - process.StartTime.ToUniversalTime()).TotalHours
            };
        }
    }
}
