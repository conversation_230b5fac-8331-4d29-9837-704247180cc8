[2025-06-17 17:17:56.047 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.051 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.053 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.053 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.055 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.058 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.093 +07:00 INF] Cache warmup completed in 42.6172ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.094 +07:00 INF] HTTP Request [f0e98ef1-436d-450a-b69a-d370ae213337]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"testuser1","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:56.0876906+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKI","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.101 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-474eda1ee1e6898b11c2f8aa53b039af-6069279155eb25ac-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"ea1a1f7b-0d81-4538-a502-22d57f803fc2","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AKI","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.103 +07:00 INF] Performance [f0e98ef1-436d-450a-b69a-d370ae213337]: POST /api/Auth/register completed in 8.5537ms with status 400. {"CorrelationId":"f0e98ef1-436d-450a-b69a-d370ae213337","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":8.5537,"StartTime":"2025-06-17T10:17:56.0944757Z","EndTime":"2025-06-17T10:17:56.1030294Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKI","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.103 +07:00 WRN] HTTP Response [f0e98ef1-436d-450a-b69a-d370ae213337]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 9.0972ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-474eda1ee1e6898b11c2f8aa53b039af-6069279155eb25ac-00","timestamp":"2025-06-17T10:17:56.101Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKI","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.105 +07:00 INF] HTTP Request [f9388e4f-6d60-4853-93ab-e6885e1c04c2]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 279 | Body: {"username":"testuser2","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:56.0876906+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKK","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.109 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-1abf7198b4901d3a07b8216034228731-9cf9f97fbfe73f59-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"ea1a1f7b-0d81-4538-a502-22d57f803fc2","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AKK","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.110 +07:00 INF] Performance [f9388e4f-6d60-4853-93ab-e6885e1c04c2]: POST /api/Auth/register completed in 3.489ms with status 400. {"CorrelationId":"f9388e4f-6d60-4853-93ab-e6885e1c04c2","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":3.489,"StartTime":"2025-06-17T10:17:56.1060706Z","EndTime":"2025-06-17T10:17:56.1095601Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":279} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AKK","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.110 +07:00 WRN] HTTP Response [f9388e4f-6d60-4853-93ab-e6885e1c04c2]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 4.404ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-1abf7198b4901d3a07b8216034228731-9cf9f97fbfe73f59-00","timestamp":"2025-06-17T10:17:56.109Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AKK","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.111 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:56.111 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
