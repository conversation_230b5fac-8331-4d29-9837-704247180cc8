using Microsoft.AspNetCore.Authorization;
using DecorStore.API.Models.Authorization;

namespace DecorStore.API.Configuration
{
    /// <summary>
    /// Authorization policies configuration
    /// </summary>
    public static class AuthorizationPolicies
    {
        // Policy names
        public const string AdminOnly = "AdminOnly";
        public const string CustomerOrAdmin = "CustomerOrAdmin";
        public const string ResourceOwner = "ResourceOwner";
        public const string CustomerResourceOwner = "CustomerResourceOwner";
        public const string OrderResourceOwner = "OrderResourceOwner";
        public const string CartResourceOwner = "CartResourceOwner";
        public const string ReviewResourceOwner = "ReviewResourceOwner";
        public const string MinimumAge18 = "MinimumAge18";
        public const string MinimumAge21 = "MinimumAge21";
        public const string BusinessHours = "BusinessHours";
        public const string AdminBusinessHours = "AdminBusinessHours";
        public const string GeolocationRestricted = "GeolocationRestricted";
        public const string TwoFactorRequired = "TwoFactorRequired";
        public const string AccountActive = "AccountActive";
        public const string ApiKeyRequired = "ApiKeyRequired";
        public const string ApiKeyWithScopes = "ApiKeyWithScopes";

        /// <summary>
        /// Configure all authorization policies
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)
        {
            services.AddAuthorization(options =>
            {
                // Basic role-based policies
                options.AddPolicy(AdminOnly, policy =>
                    policy.RequireRole("Admin"));

                options.AddPolicy(CustomerOrAdmin, policy =>
                    policy.RequireRole("Customer", "Admin"));

                // Resource ownership policies
                options.AddPolicy(ResourceOwner, policy =>
                    policy.Requirements.Add(new ResourceOwnerRequirement()));

                options.AddPolicy(CustomerResourceOwner, policy =>
                    policy.Requirements.Add(new ResourceOwnerRequirement("customer")));

                options.AddPolicy(OrderResourceOwner, policy =>
                    policy.Requirements.Add(new ResourceOwnerRequirement("order")));

                options.AddPolicy(CartResourceOwner, policy =>
                    policy.Requirements.Add(new ResourceOwnerRequirement("cart")));

                options.AddPolicy(ReviewResourceOwner, policy =>
                    policy.Requirements.Add(new ResourceOwnerRequirement("review")));

                // Age-based policies
                options.AddPolicy(MinimumAge18, policy =>
                    policy.Requirements.Add(new MinimumAgeRequirement(18)));

                options.AddPolicy(MinimumAge21, policy =>
                    policy.Requirements.Add(new MinimumAgeRequirement(21)));

                // Business hours policies
                options.AddPolicy(BusinessHours, policy =>
                    policy.Requirements.Add(new BusinessHoursRequirement(
                        new TimeSpan(9, 0, 0), // 9 AM
                        new TimeSpan(17, 0, 0), // 5 PM
                        DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, 
                        DayOfWeek.Thursday, DayOfWeek.Friday)));

                options.AddPolicy(AdminBusinessHours, policy =>
                {
                    policy.RequireRole("Admin");
                    policy.Requirements.Add(new BusinessHoursRequirement(
                        new TimeSpan(8, 0, 0), // 8 AM
                        new TimeSpan(18, 0, 0), // 6 PM
                        DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, 
                        DayOfWeek.Thursday, DayOfWeek.Friday));
                });

                // Geolocation policies
                options.AddPolicy(GeolocationRestricted, policy =>
                    policy.Requirements.Add(new GeolocationRequirement(
                        allowedCountries: new[] { "US", "CA", "GB", "AU" },
                        blockedCountries: new[] { "XX", "YY" })));

                // Two-factor authentication policy
                options.AddPolicy(TwoFactorRequired, policy =>
                    policy.Requirements.Add(new TwoFactorRequirement()));

                // Account status policy
                options.AddPolicy(AccountActive, policy =>
                    policy.Requirements.Add(new AccountStatusRequirement()));

                // API key policies
                options.AddPolicy(ApiKeyRequired, policy =>
                    policy.Requirements.Add(new ApiKeyRequirement()));

                options.AddPolicy(ApiKeyWithScopes, policy =>
                    policy.Requirements.Add(new ApiKeyRequirement(
                        requiredScopes: new[] { "read", "write" })));

                // Combined policies for sensitive operations
                options.AddPolicy("SensitiveAdminOperation", policy =>
                {
                    policy.RequireRole("Admin");
                    policy.Requirements.Add(new TwoFactorRequirement());
                    policy.Requirements.Add(new AccountStatusRequirement());
                });

                options.AddPolicy("CustomerDataAccess", policy =>
                {
                    policy.RequireRole("Customer", "Admin");
                    policy.Requirements.Add(new ResourceOwnerRequirement("customer"));
                    policy.Requirements.Add(new AccountStatusRequirement());
                });

                options.AddPolicy("OrderManagement", policy =>
                {
                    policy.RequireRole("Customer", "Admin");
                    policy.Requirements.Add(new ResourceOwnerRequirement("order"));
                    policy.Requirements.Add(new AccountStatusRequirement());
                });

                options.AddPolicy("AgeRestrictedContent", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.Requirements.Add(new MinimumAgeRequirement(18));
                    policy.Requirements.Add(new AccountStatusRequirement());
                });

                options.AddPolicy("RestrictedBusinessOperations", policy =>
                {
                    policy.RequireRole("Admin", "Manager");
                    policy.Requirements.Add(new BusinessHoursRequirement(
                        new TimeSpan(9, 0, 0),
                        new TimeSpan(17, 0, 0)));
                    policy.Requirements.Add(new TwoFactorRequirement());
                });
            });

            return services;
        }

        /// <summary>
        /// Register all authorization handlers
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddAuthorizationHandlers(this IServiceCollection services)
        {
            services.AddScoped<IAuthorizationHandler, Services.Authorization.ResourceOwnerAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, Services.Authorization.MinimumAgeAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, Services.Authorization.BusinessHoursAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, Services.Authorization.GeolocationAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, Services.Authorization.TwoFactorAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, Services.Authorization.AccountStatusAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, Services.Authorization.ApiKeyAuthorizationHandler>();

            return services;
        }
    }

    /// <summary>
    /// Extension methods for authorization attributes
    /// </summary>
    public static class AuthorizeExtensions
    {
        /// <summary>
        /// Authorize with resource ownership validation
        /// </summary>
        public static AuthorizeAttribute ResourceOwner(string resourceType = "resource", string resourceIdParameter = "id")
        {
            return new AuthorizeAttribute { Policy = $"ResourceOwner_{resourceType}_{resourceIdParameter}" };
        }

        /// <summary>
        /// Authorize with minimum age requirement
        /// </summary>
        public static AuthorizeAttribute MinimumAge(int age)
        {
            return new AuthorizeAttribute { Policy = $"MinimumAge{age}" };
        }

        /// <summary>
        /// Authorize with business hours requirement
        /// </summary>
        public static AuthorizeAttribute BusinessHours()
        {
            return new AuthorizeAttribute { Policy = AuthorizationPolicies.BusinessHours };
        }

        /// <summary>
        /// Authorize with two-factor authentication requirement
        /// </summary>
        public static AuthorizeAttribute TwoFactor()
        {
            return new AuthorizeAttribute { Policy = AuthorizationPolicies.TwoFactorRequired };
        }

        /// <summary>
        /// Authorize with API key requirement
        /// </summary>
        public static AuthorizeAttribute ApiKey(params string[] scopes)
        {
            return new AuthorizeAttribute 
            { 
                Policy = scopes.Length > 0 ? AuthorizationPolicies.ApiKeyWithScopes : AuthorizationPolicies.ApiKeyRequired 
            };
        }
    }
}
