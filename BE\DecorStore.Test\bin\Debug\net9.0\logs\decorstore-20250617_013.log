[2025-06-17 17:17:54.760 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.763 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.764 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.765 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.766 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.769 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.793 +07:00 INF] HTTP Request [71cfc939-2772-422e-bdc0-612f9fc03c58]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 85 | Body: {"email":"<EMAIL>","password":"WrongPassword123!","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK0","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.797 +07:00 INF] Cache warmup completed in 33.709ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.815 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-ef009fff39ee8ac24f4d1329e5b01fad-a7107642b62b5b65-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"08f14081-56d6-416d-92c3-ba94cd80bc58","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDH0SJ5AK0","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.821 +07:00 INF] Performance [71cfc939-2772-422e-bdc0-612f9fc03c58]: POST /api/Auth/login completed in 27.5661ms with status 400. {"CorrelationId":"71cfc939-2772-422e-bdc0-612f9fc03c58","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":27.5661,"StartTime":"2025-06-17T10:17:54.7939454Z","EndTime":"2025-06-17T10:17:54.8215119Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":85} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AK0","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.821 +07:00 WRN] HTTP Response [71cfc939-2772-422e-bdc0-612f9fc03c58]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 28.2365ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-ef009fff39ee8ac24f4d1329e5b01fad-a7107642b62b5b65-00","timestamp":"2025-06-17T10:17:54.815Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK0","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.824 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.824 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
