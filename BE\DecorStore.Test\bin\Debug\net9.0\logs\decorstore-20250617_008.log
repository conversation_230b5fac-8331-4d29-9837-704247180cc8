[2025-06-17 17:15:30.903 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.906 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.907 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.908 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.909 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.921 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.933 +07:00 INF] Cache warmup completed in 26.3514ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.934 +07:00 INF] HTTP Request [0a87ba31-ba52-48ae-852f-c5b7e8f40cda]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 283 | Body: {"username":"wrongpass","email":"<EMAIL>","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.9290455+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIN","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.938 +07:00 INF] Performance [0a87ba31-ba52-48ae-852f-c5b7e8f40cda]: POST /api/Auth/register completed in 4.5916ms with status 400. {"CorrelationId":"0a87ba31-ba52-48ae-852f-c5b7e8f40cda","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":4.5916,"StartTime":"2025-06-17T10:15:30.9342464Z","EndTime":"2025-06-17T10:15:30.9388381Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":283} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIN","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.939 +07:00 WRN] HTTP Response [0a87ba31-ba52-48ae-852f-c5b7e8f40cda]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 4.9278ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-df9f118a2519e2bf6adde5275ec08a03-67f0f1b558a18247-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIN","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.941 +07:00 INF] HTTP Request [8191c856-4ae6-48ca-9748-faa2e0da02d5]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 283 | Body: {"username":"wrongpass","email":"<EMAIL>","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.9395943+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIP","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.942 +07:00 INF] Performance [8191c856-4ae6-48ca-9748-faa2e0da02d5]: POST /api/Auth/register completed in 0.9963ms with status 400. {"CorrelationId":"8191c856-4ae6-48ca-9748-faa2e0da02d5","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":0.9963,"StartTime":"2025-06-17T10:15:30.9413024Z","EndTime":"2025-06-17T10:15:30.9422987Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":283} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIP","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.942 +07:00 WRN] HTTP Response [8191c856-4ae6-48ca-9748-faa2e0da02d5]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 1.3534ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-d3f71b68617638201f655054518435d0-29ba1e630a36036b-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIP","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.943 +07:00 INF] HTTP Request [d5436a3c-5065-4122-bbe1-a32b3dee2e62]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 82 | Body: {"email":"<EMAIL>","password":"TestPassword123!","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIR","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.948 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-180afce938447ca5cc8b1008d8eb5fa8-faca171d1d777a55-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"1278f46e-a00d-4f35-a3e1-35eb202a8c13","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDGVHETHIR","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.949 +07:00 INF] Performance [d5436a3c-5065-4122-bbe1-a32b3dee2e62]: POST /api/Auth/login completed in 6.267ms with status 400. {"CorrelationId":"d5436a3c-5065-4122-bbe1-a32b3dee2e62","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":6.267,"StartTime":"2025-06-17T10:15:30.9431372Z","EndTime":"2025-06-17T10:15:30.9494042Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":82} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIR","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.949 +07:00 WRN] HTTP Response [d5436a3c-5065-4122-bbe1-a32b3dee2e62]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 6.5219ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-180afce938447ca5cc8b1008d8eb5fa8-faca171d1d777a55-00","timestamp":"2025-06-17T10:15:30.949Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIR","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.951 +07:00 INF] HTTP Request [530cd704-7dae-4072-9ca2-f03939d1c797]: POST /api/Auth/change-password  | Content-Type: application/json; charset=utf-8 | Content-Length: 110 | Body: {"currentPassword":"WrongPassword123!","newPassword":"NewPassword123!","confirmNewPassword":"NewPassword123!"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIT","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.951 +07:00 INF] Performance [530cd704-7dae-4072-9ca2-f03939d1c797]: POST /api/Auth/change-password completed in 0.5719ms with status 401. {"CorrelationId":"530cd704-7dae-4072-9ca2-f03939d1c797","Method":"POST","Path":"/api/Auth/change-password","QueryString":"","StatusCode":401,"DurationMs":0.5719,"StartTime":"2025-06-17T10:15:30.9511395Z","EndTime":"2025-06-17T10:15:30.9517114Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":110} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHIT","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.951 +07:00 WRN] HTTP Response [530cd704-7dae-4072-9ca2-f03939d1c797]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 0.8737ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHIT","RequestPath":"/api/Auth/change-password","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.953 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.953 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
