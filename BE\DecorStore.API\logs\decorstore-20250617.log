[2025-06-17 09:26:17.368 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:17.394 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.473 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.478 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.483 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.486 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.491 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.495 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.499 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.505 +07:00 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development. {"EventId":{"Id":10400,"Name":"Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.867 +07:00 INF] Cache warmup completed in 1471.6732ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.878 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.880 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.912 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:26:18.936 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:27:16.767 +07:00 INF] Request body sanitized [b2aa0762-b67c-4eb4-b15b-c307265a2714] {"SourceContext":"DecorStore.API.Middleware.InputSanitizationMiddleware","RequestId":"0HNDD8PH27950:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD8PH27950","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:27:16.779 +07:00 INF] HTTP Request [ffd3ccb1-21b9-4705-bf2b-cb533162deee]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 253 | Body: {"username":"truongadmin","email":"<EMAIL>","password":"123456","confirmPassword":"123456","firstName":"truong","lastName":"admin","phone":"0945671745","dateOfBirth":"2000-06-17T02:26:42.814Z","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD8PH27950:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD8PH27950","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:27:18.273 +07:00 WRN] Performance [ffd3ccb1-21b9-4705-bf2b-cb533162deee]: POST /api/Auth/register completed in 1482.0707ms with status 200. {"CorrelationId":"ffd3ccb1-21b9-4705-bf2b-cb533162deee","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":200,"DurationMs":1482.0707,"StartTime":"2025-06-17T02:27:16.7855251Z","EndTime":"2025-06-17T02:27:18.2675959Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":253} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD8PH27950:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD8PH27950","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:27:18.279 +07:00 WRN] Slow request detected [ffd3ccb1-21b9-4705-bf2b-cb533162deee]: POST /api/Auth/register took 1482.0707ms {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD8PH27950:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD8PH27950","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:27:18.283 +07:00 INF] HTTP Response [ffd3ccb1-21b9-4705-bf2b-cb533162deee]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 1504.4818ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD8PH27950:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD8PH27950","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 09:27:18.286 +07:00 ERR] Error in input sanitization middleware [b2aa0762-b67c-4eb4-b15b-c307265a2714] {"SourceContext":"DecorStore.API.Middleware.InputSanitizationMiddleware","RequestId":"0HNDD8PH27950:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD8PH27950","Application":"DecorStore.API","Environment":"Development"}
FluentValidation.AsyncValidatorInvokedSynchronouslyException: Validator "RegisterValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.
   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207
   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153
   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitComplexType(IValidationStrategy defaultStrategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45
   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63
   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.InputSanitizationMiddleware.SanitizeJsonRequest(HttpContext context, String correlationId) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\InputSanitizationMiddleware.cs:line 208
   at DecorStore.API.Middleware.InputSanitizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\InputSanitizationMiddleware.cs:line 90
[2025-06-17 09:27:18.320 +07:00 ERR] Unhandled exception occurred [7d0598ab326e4d069fceb59cc78d7544]: ObjectDisposedException - Cannot access a closed Stream.. Context: {"CorrelationId":"7d0598ab326e4d069fceb59cc78d7544","ExceptionType":"ObjectDisposedException","Message":"Cannot access a closed Stream.","StackTrace":"   at System.IO.MemoryStream.Write(Byte[] buffer, Int32 offset, Int32 count)\r\n   at System.IO.MemoryStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)\r\n--- End of stack trace from previous location ---\r\n   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)\r\n   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)\r\n   at System.Threading.Tasks.ValueTask`1.GetTaskForValueTaskSource(IValueTaskSource`1 t)\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.InputSanitizationMiddleware.WriteErrorResponse(HttpContext context, String message, Int32 statusCode) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\InputSanitizationMiddleware.cs:line 361\r\n   at DecorStore.API.Middleware.InputSanitizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\InputSanitizationMiddleware.cs:line 104\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 64\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"Anonymous","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","IpAddress":"::1","RequestPath":"/api/Auth/register","RequestMethod":"POST","QueryString":"","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDD8PH27950:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD8PH27950","Application":"DecorStore.API","Environment":"Development"}
System.ObjectDisposedException: Cannot access a closed Stream.
   at System.IO.MemoryStream.Write(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.MemoryStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask`1.GetTaskForValueTaskSource(IValueTaskSource`1 t)
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.InputSanitizationMiddleware.WriteErrorResponse(HttpContext context, String message, Int32 statusCode) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\InputSanitizationMiddleware.cs:line 361
   at DecorStore.API.Middleware.InputSanitizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\InputSanitizationMiddleware.cs:line 104
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 64
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 09:31:18.899 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.610 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.636 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.930 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.936 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.940 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.944 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.949 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.953 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.957 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:24.965 +07:00 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development. {"EventId":{"Id":10400,"Name":"Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:25.352 +07:00 INF] Cache warmup completed in 714.0278ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:25.361 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:25.364 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:25.377 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:11:25.402 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:14:19.973 +07:00 INF] HTTP Request [fb7a6e94-4a10-4eb9-9b0e-ec917cc2ba76]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 286 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "123",
  "confirmPassword": "123",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9IIVVBIS:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9IIVVBIS","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:14:20.046 +07:00 INF] Performance [fb7a6e94-4a10-4eb9-9b0e-ec917cc2ba76]: POST /api/Auth/register completed in 61.4615ms with status 200. {"CorrelationId":"fb7a6e94-4a10-4eb9-9b0e-ec917cc2ba76","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":200,"DurationMs":61.4615,"StartTime":"2025-06-17T03:14:19.9786802Z","EndTime":"2025-06-17T03:14:20.0401410Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":286} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9IIVVBIS:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9IIVVBIS","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:14:20.053 +07:00 INF] HTTP Response [fb7a6e94-4a10-4eb9-9b0e-ec917cc2ba76]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 82.4011ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9IIVVBIS:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9IIVVBIS","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:14:20.076 +07:00 ERR] Unhandled exception occurred [5b86e78c7fde4837b4232abc8c07d83a]: AsyncValidatorInvokedSynchronouslyException - Validator "RegisterValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.. Context: {"CorrelationId":"5b86e78c7fde4837b4232abc8c07d83a","ExceptionType":"AsyncValidatorInvokedSynchronouslyException","Message":"Validator \"RegisterValidator\" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.","StackTrace":"   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207\r\n   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153\r\n   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitComplexType(IValidationStrategy defaultStrategy)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)\r\n   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\PerformanceLoggingMiddleware.cs:line 31\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 38\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 51\r\n   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\CorrelationIdMiddleware.cs:line 29\r\n   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)\r\n   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ApiKeyRateLimitingMiddleware.cs:line 101\r\n   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Extensions\\SecurityExtensions.cs:line 161\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 64\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"Anonymous","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","IpAddress":"::1","RequestPath":"/api/Auth/register","RequestMethod":"POST","QueryString":"","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDD9IIVVBIS:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9IIVVBIS","Application":"DecorStore.API","Environment":"Development"}
FluentValidation.AsyncValidatorInvokedSynchronouslyException: Validator "RegisterValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.
   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207
   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153
   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitComplexType(IValidationStrategy defaultStrategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45
   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63
   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 64
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 10:16:34.347 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.372 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.634 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.637 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.640 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.642 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.644 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.647 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.650 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.655 +07:00 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development. {"EventId":{"Id":10400,"Name":"Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:34.996 +07:00 INF] Cache warmup completed in 622.6963ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:35.006 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:35.008 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:35.022 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:35.044 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:47.739 +07:00 INF] HTTP Request [26d701f9-8260-4d85-a95f-73cd094b64d8]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 286 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "123",
  "confirmPassword": "123",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:47.847 +07:00 INF] Performance [26d701f9-8260-4d85-a95f-73cd094b64d8]: POST /api/Auth/register completed in 96.788ms with status 400. {"CorrelationId":"26d701f9-8260-4d85-a95f-73cd094b64d8","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":96.788,"StartTime":"2025-06-17T03:16:47.7438396Z","EndTime":"2025-06-17T03:16:47.8406269Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":286} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:16:47.855 +07:00 WRN] HTTP Response [26d701f9-8260-4d85-a95f-73cd094b64d8]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 595 | Duration: 118.4547ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["The field Password must be a string with a minimum length of 6 and a maximum length of 100.","Password must be at least 8 characters long","Password must contain at least one uppercase letter","Password must contain at least one lowercase letter","Password must contain at least one special character (!@#$%^&*)","Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-85252745bfeff693d781069a57cf2167-dcac9d1800de296e-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:17:10.210 +07:00 INF] HTTP Request [6be86b07-64ff-489e-8754-a6216d729db1]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 298 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "123456aA@",
  "confirmPassword": "123456aA@",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000002","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:17:10.227 +07:00 INF] Performance [6be86b07-64ff-489e-8754-a6216d729db1]: POST /api/Auth/register completed in 6.4686ms with status 400. {"CorrelationId":"6be86b07-64ff-489e-8754-a6216d729db1","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":6.4686,"StartTime":"2025-06-17T03:17:10.2185745Z","EndTime":"2025-06-17T03:17:10.2250457Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":298} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000002","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:17:10.240 +07:00 WRN] HTTP Response [6be86b07-64ff-489e-8754-a6216d729db1]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 30.2322ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-763cbac93a7428595fed9a7089f77f6e-26e553665d2bdb10-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000002","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:17:35.664 +07:00 INF] HTTP Request [acfbb22d-1bef-4666-8fb6-d25f3aff6554]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 300 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "Anhvip@522",
  "confirmPassword": "Anhvip@522",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000003","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:17:35.800 +07:00 WRN] Request failed with error: Execution strategy failed: An error was generated for warning 'Microsoft.EntityFrameworkCore.Database.Transaction.TransactionIgnoredWarning': Transactions are not supported by the in-memory store. See https://go.microsoft.com/fwlink/?LinkId=800142 This exception can be suppressed or logged by passing event ID 'InMemoryEventId.TransactionIgnoredWarning' to the 'ConfigureWarnings' method in 'DbContext.OnConfiguring' or 'AddDbContext'., ErrorCode: EXECUTION_STRATEGY_ERROR, CorrelationId: 00-74942e0f38a78e783aa0ca05de027314-cdfc8359669d4651-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"b770b98e-9bcd-44e7-9c91-befce17e0bbf","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDD9LIHOVML:00000003","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:17:35.809 +07:00 INF] Performance [acfbb22d-1bef-4666-8fb6-d25f3aff6554]: POST /api/Auth/register completed in 137.9575ms with status 400. {"CorrelationId":"acfbb22d-1bef-4666-8fb6-d25f3aff6554","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":137.9575,"StartTime":"2025-06-17T03:17:35.6710349Z","EndTime":"2025-06-17T03:17:35.8089922Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":300} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000003","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:17:35.815 +07:00 WRN] HTTP Response [acfbb22d-1bef-4666-8fb6-d25f3aff6554]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 600 | Duration: 150.9537ms | Body: {"error":"Execution strategy failed: An error was generated for warning 'Microsoft.EntityFrameworkCore.Database.Transaction.TransactionIgnoredWarning': Transactions are not supported by the in-memory store. See https://go.microsoft.com/fwlink/?LinkId=800142 This exception can be suppressed or logged by passing event ID 'InMemoryEventId.TransactionIgnoredWarning' to the 'ConfigureWarnings' method in 'DbContext.OnConfiguring' or 'AddDbContext'.","errorCode":"EXECUTION_STRATEGY_ERROR","correlationId":"00-74942e0f38a78e783aa0ca05de027314-cdfc8359669d4651-00","timestamp":"2025-06-17T03:17:35.803Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9LIHOVML:00000003","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9LIHOVML","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:21:35.026 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.406 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.431 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.697 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.700 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.702 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.705 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.707 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.709 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.712 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:03.717 +07:00 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development. {"EventId":{"Id":10400,"Name":"Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:04.047 +07:00 INF] Cache warmup completed in 614.4032ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:04.055 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:04.057 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:04.069 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:04.090 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:21.340 +07:00 INF] HTTP Request [90c1b411-171e-4caa-97d9-1ad8e72afacb]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 300 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "Anhvip@522",
  "confirmPassword": "Anhvip@522",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9OLV4AM3:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9OLV4AM3","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:21.764 +07:00 WRN] Request failed with error: Failed to register user: Missing type map configuration or unsupported mapping.

Mapping types:
User -> UserDTO
DecorStore.API.Models.User -> DecorStore.API.DTOs.UserDTO, ErrorCode: REGISTRATION_ERROR, CorrelationId: 00-d0e1d98b45274d03c5919887a646f5d9-ca2ec4772a54e0a5-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"b7a13a25-b7dc-46f4-8eca-85c2258bc98b","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDD9OLV4AM3:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9OLV4AM3","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:21.783 +07:00 INF] Performance [90c1b411-171e-4caa-97d9-1ad8e72afacb]: POST /api/Auth/register completed in 427.4893ms with status 400. {"CorrelationId":"90c1b411-171e-4caa-97d9-1ad8e72afacb","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":427.4893,"StartTime":"2025-06-17T03:22:21.3497584Z","EndTime":"2025-06-17T03:22:21.7772476Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":300} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9OLV4AM3:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9OLV4AM3","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:22:21.791 +07:00 WRN] HTTP Response [90c1b411-171e-4caa-97d9-1ad8e72afacb]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 339 | Duration: 453.7333ms | Body: {"error":"Failed to register user: Missing type map configuration or unsupported mapping.\r\n\r\nMapping types:\r\nUser -> UserDTO\r\nDecorStore.API.Models.User -> DecorStore.API.DTOs.UserDTO","errorCode":"REGISTRATION_ERROR","correlationId":"00-d0e1d98b45274d03c5919887a646f5d9-ca2ec4772a54e0a5-00","timestamp":"2025-06-17T03:22:21.767Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9OLV4AM3:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9OLV4AM3","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:27.843 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:27.868 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.123 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.126 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.129 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.131 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.134 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.136 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.139 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.144 +07:00 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development. {"EventId":{"Id":10400,"Name":"Microsoft.EntityFrameworkCore.Infrastructure.SensitiveDataLoggingEnabledWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.494 +07:00 INF] Cache warmup completed in 623.8944ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.503 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.505 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.518 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:28.540 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:32.892 +07:00 INF] HTTP Request [e955450a-f6ad-4c29-a67f-fc7b3866b24e]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 300 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "Anhvip@522",
  "confirmPassword": "Anhvip@522",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39M:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9PT5O39M","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:33.240 +07:00 INF] Performance [e955450a-f6ad-4c29-a67f-fc7b3866b24e]: POST /api/Auth/register completed in 337.7625ms with status 201. {"CorrelationId":"e955450a-f6ad-4c29-a67f-fc7b3866b24e","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":337.7625,"StartTime":"2025-06-17T03:24:32.8974082Z","EndTime":"2025-06-17T03:24:33.2351705Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":300} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9PT5O39M:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9PT5O39M","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:33.248 +07:00 INF] HTTP Response [e955450a-f6ad-4c29-a67f-fc7b3866b24e]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 426 | Duration: 358.5128ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.laCSd_ZUPr6oDgmvNuKShdr2zVleAvOlRj6IbCVYR2Y","user":{"id":1,"username":"truongadmin","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39M:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDD9PT5O39M","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:49.150 +07:00 INF] HTTP Request [f275c92c-d5af-46b5-bcfa-93ef44711811]: POST /api/Auth/make-admin  | Content-Type: application/json | Content-Length: 38 | Body: {
  "email": "<EMAIL>"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39N:00000001","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDD9PT5O39N","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:49.163 +07:00 INF] Performance [f275c92c-d5af-46b5-bcfa-93ef44711811]: POST /api/Auth/make-admin completed in 8.4508ms with status 401. {"CorrelationId":"f275c92c-d5af-46b5-bcfa-93ef44711811","Method":"POST","Path":"/api/Auth/make-admin","QueryString":"","StatusCode":401,"DurationMs":8.4508,"StartTime":"2025-06-17T03:24:49.1543779Z","EndTime":"2025-06-17T03:24:49.1628307Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":38} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9PT5O39N:00000001","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDD9PT5O39N","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:24:49.171 +07:00 WRN] HTTP Response [f275c92c-d5af-46b5-bcfa-93ef44711811]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 20.5913ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39N:00000001","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDD9PT5O39N","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:26:45.436 +07:00 INF] HTTP Request [b15b84f3-dce5-4b4f-97bb-c80d8b2faa3b]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 88 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522",
  "rememberMe": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39N:00000002","RequestPath":"/api/Auth/login","ConnectionId":"0HNDD9PT5O39N","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:26:45.709 +07:00 INF] Performance [b15b84f3-dce5-4b4f-97bb-c80d8b2faa3b]: POST /api/Auth/login completed in 269.1898ms with status 200. {"CorrelationId":"b15b84f3-dce5-4b4f-97bb-c80d8b2faa3b","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":269.1898,"StartTime":"2025-06-17T03:26:45.4397885Z","EndTime":"2025-06-17T03:26:45.7089785Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":88} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9PT5O39N:00000002","RequestPath":"/api/Auth/login","ConnectionId":"0HNDD9PT5O39N","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:26:45.715 +07:00 INF] HTTP Response [b15b84f3-dce5-4b4f-97bb-c80d8b2faa3b]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 426 | Duration: 278.8343ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.269zRa5PFV83iKCTtMgGfekJn5lj_FpjlDM198OyEE8","user":{"id":1,"username":"truongadmin","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39N:00000002","RequestPath":"/api/Auth/login","ConnectionId":"0HNDD9PT5O39N","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:28:59.037 +07:00 INF] HTTP Request [ae20fc74-dd13-4e26-a2ce-cf0fdf25e607]: GET /api/Auth/user  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39O:00000001","RequestPath":"/api/Auth/user","ConnectionId":"0HNDD9PT5O39O","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:28:59.076 +07:00 INF] Performance [ae20fc74-dd13-4e26-a2ce-cf0fdf25e607]: GET /api/Auth/user completed in 29.3462ms with status 200. {"CorrelationId":"ae20fc74-dd13-4e26-a2ce-cf0fdf25e607","Method":"GET","Path":"/api/Auth/user","QueryString":"","StatusCode":200,"DurationMs":29.3462,"StartTime":"2025-06-17T03:28:59.0466777Z","EndTime":"2025-06-17T03:28:59.0760233Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9PT5O39O:00000001","RequestPath":"/api/Auth/user","ConnectionId":"0HNDD9PT5O39O","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:28:59.088 +07:00 INF] HTTP Response [ae20fc74-dd13-4e26-a2ce-cf0fdf25e607]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 79 | Duration: 51.1232ms | Body: {"id":1,"username":"truongadmin","email":"<EMAIL>","role":"User"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39O:00000001","RequestPath":"/api/Auth/user","ConnectionId":"0HNDD9PT5O39O","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:29:28.523 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:34:28.521 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:39:28.508 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:44:28.519 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:49:28.519 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:54:28.512 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 10:59:28.509 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:03:03.267 +07:00 INF] HTTP Request [b197b0ce-b483-4a2c-b193-f81ce5364627]: GET /api/Auth/user  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39P:00000001","RequestPath":"/api/Auth/user","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:03:03.282 +07:00 INF] Performance [b197b0ce-b483-4a2c-b193-f81ce5364627]: GET /api/Auth/user completed in 10.8375ms with status 200. {"CorrelationId":"b197b0ce-b483-4a2c-b193-f81ce5364627","Method":"GET","Path":"/api/Auth/user","QueryString":"","StatusCode":200,"DurationMs":10.8375,"StartTime":"2025-06-17T04:03:03.2716058Z","EndTime":"2025-06-17T04:03:03.2824436Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9PT5O39P:00000001","RequestPath":"/api/Auth/user","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:03:03.292 +07:00 INF] HTTP Response [b197b0ce-b483-4a2c-b193-f81ce5364627]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 79 | Duration: 25.0565ms | Body: {"id":1,"username":"truongadmin","email":"<EMAIL>","role":"User"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39P:00000001","RequestPath":"/api/Auth/user","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:04:28.510 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:05:10.824 +07:00 INF] HTTP Request [2e5abfea-9699-415a-9772-a333a3df2e0b]: GET /api/Category  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39P:00000002","RequestPath":"/api/Category","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:05:11.953 +07:00 WRN] Performance [2e5abfea-9699-415a-9772-a333a3df2e0b]: GET /api/Category completed in 1125.8048ms with status 200. {"CorrelationId":"2e5abfea-9699-415a-9772-a333a3df2e0b","Method":"GET","Path":"/api/Category","QueryString":"","StatusCode":200,"DurationMs":1125.8048,"StartTime":"2025-06-17T04:05:10.8279567Z","EndTime":"2025-06-17T04:05:11.9537615Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9PT5O39P:00000002","RequestPath":"/api/Category","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:05:11.959 +07:00 WRN] Slow request detected [2e5abfea-9699-415a-9772-a333a3df2e0b]: GET /api/Category took 1125.8048ms {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDD9PT5O39P:00000002","RequestPath":"/api/Category","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:05:11.962 +07:00 INF] HTTP Response [2e5abfea-9699-415a-9772-a333a3df2e0b]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 1137.8806ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDD9PT5O39P:00000002","RequestPath":"/api/Category","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:05:11.980 +07:00 ERR] Unhandled exception occurred [f575757de31945c896ab7aab7ac8cbb8]: InvalidOperationException - 'VaryByQueryKeys' requires the response cache middleware.. Context: {"CorrelationId":"f575757de31945c896ab7aab7ac8cbb8","ExceptionType":"InvalidOperationException","Message":"'VaryByQueryKeys' requires the response cache middleware.","StackTrace":"   at Microsoft.AspNetCore.Mvc.Filters.ResponseCacheFilterExecutor.Execute(FilterContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\PerformanceLoggingMiddleware.cs:line 31\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 38\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 51\r\n   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\CorrelationIdMiddleware.cs:line 29\r\n   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)\r\n   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ApiKeyRateLimitingMiddleware.cs:line 101\r\n   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Extensions\\SecurityExtensions.cs:line 161\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 99\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"truongadmin","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","IpAddress":"::1","RequestPath":"/api/Category","RequestMethod":"GET","QueryString":"","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDD9PT5O39P:00000002","RequestPath":"/api/Category","ConnectionId":"0HNDD9PT5O39P","Application":"DecorStore.API","Environment":"Development"}
System.InvalidOperationException: 'VaryByQueryKeys' requires the response cache middleware.
   at Microsoft.AspNetCore.Mvc.Filters.ResponseCacheFilterExecutor.Execute(FilterContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 99
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 11:09:28.517 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:35.913 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:35.939 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:35.942 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:35.945 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:35.947 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:35.950 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:35.952 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:53.798 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:53.823 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.163 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.166 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.169 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.171 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.174 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.176 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.180 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.530 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.533 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.545 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.598 +07:00 ERR] Failed executing DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[Id], [c].[CreatedAt], [c].[Description], [c].[IsDeleted], [c].[Name], [c].[ParentId], [c].[Slug], [c].[SortOrder], [c].[UpdatedAt]
FROM [Categories] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit) AND [c].[ParentId] IS NULL AND [c].[IsDeleted] = CAST(0 AS bit)
ORDER BY [c].[SortOrder], [c].[Name], [c].[Id] {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.654 +07:00 ERR] Failed executing DbCommand (55ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedByIp], [r].[ExpiryDate], [r].[IsDeleted], [r].[IsRevoked], [r].[IsUsed], [r].[JwtId], [r].[ReplacedByToken], [r].[RevokedAt], [r].[RevokedByIp], [r].[RevokedReason], [r].[Token], [r].[TokenFamily], [r].[TokenVersion], [r].[UpdatedAt], [r].[UserAgent], [r].[UserId]
FROM [RefreshTokens] AS [r]
WHERE [r].[ExpiryDate] < @__cutoffTime_0 OR [r].[IsRevoked] = CAST(1 AS bit) OR [r].[IsUsed] = CAST(1 AS bit) {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.663 +07:00 ERR] An exception occurred while iterating over the results of a query for context type 'DecorStore.API.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
Invalid column name 'SortOrder'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:bc51929f-125b-4d6f-96cf-33f81c6441df
Error Number:207,State:1,Class:16 {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
Invalid column name 'SortOrder'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:bc51929f-125b-4d6f-96cf-33f81c6441df
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.663 +07:00 ERR] An exception occurred while iterating over the results of a query for context type 'DecorStore.API.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'RefreshTokens'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16 {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'RefreshTokens'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16
[2025-06-17 11:11:54.672 +07:00 WRN] Failed to warm up categories cache {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
Invalid column name 'SortOrder'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DecorStore.API.Repositories.CategoryRepository.<GetRootCategoriesWithChildrenAsync>b__3_0() in D:\Personal Projects\Decor\BE\DecorStore.API\Repositories\CategoryRepository.cs:line 40
   at DecorStore.API.Services.CacheService.GetOrCreateAsync[T](String key, Func`1 factory, TimeSpan expiration, CacheItemPriority priority, String tag) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\CacheService.cs:line 109
   at DecorStore.API.Repositories.CategoryRepository.GetRootCategoriesWithChildrenAsync() in D:\Personal Projects\Decor\BE\DecorStore.API\Repositories\CategoryRepository.cs:line 38
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.<>c__DisplayClass7_0.<<WarmupCategories>b__0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 94
--- End of stack trace from previous location ---
   at DecorStore.API.Services.CacheService.GetOrCreateAsync[T](String key, Func`1 factory, Nullable`1 expiration) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\CacheService.cs:line 82
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.WarmupCategories(CancellationToken cancellationToken) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 92
ClientConnectionId:bc51929f-125b-4d6f-96cf-33f81c6441df
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.675 +07:00 ERR] Error during token cleanup {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'RefreshTokens'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DecorStore.API.Services.TokenCleanupService.CleanupExpiredTokensAsync() in D:\Personal Projects\Decor\BE\DecorStore.API\Services\TokenCleanupService.cs:line 56
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16
[2025-06-17 11:11:54.719 +07:00 ERR] Failed executing DbCommand (1ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Action], [s].[CorrelationId], [s].[CreatedAt], [s].[Details], [s].[ErrorCode], [s].[ErrorMessage], [s].[EventCategory], [s].[EventType], [s].[GeolocationCity], [s].[GeolocationCountry], [s].[HttpMethod], [s].[IpAddress], [s].[IsAnomaly], [s].[IsDeleted], [s].[IsProcessed], [s].[ProcessedAt], [s].[ProcessedBy], [s].[Recommendations], [s].[RequestPath], [s].[RequiresInvestigation], [s].[Resource], [s].[ResponseStatusCode], [s].[ResponseTimeMs], [s].[RiskScore], [s].[SessionId], [s].[Severity], [s].[Success], [s].[ThreatType], [s].[Timestamp], [s].[UpdatedAt], [s].[UserAgent], [s].[UserId], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[Timestamp] < @__cutoffTime_0 AND [s].[RequiresInvestigation] = CAST(0 AS bit) {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.724 +07:00 ERR] An exception occurred while iterating over the results of a query for context type 'DecorStore.API.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'SecurityEvents'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16 {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'SecurityEvents'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16
[2025-06-17 11:11:54.724 +07:00 ERR] Failed executing DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p0].[Id], [p0].[AverageRating], [p0].[CategoryId], [p0].[CreatedAt], [p0].[Description], [p0].[IsActive], [p0].[IsDeleted], [p0].[IsFeatured], [p0].[Name], [p0].[OriginalPrice], [p0].[Price], [p0].[SKU], [p0].[Slug], [p0].[StockQuantity], [p0].[UpdatedAt], [c0].[Id], [c0].[CreatedAt], [c0].[Description], [c0].[IsDeleted], [c0].[Name], [c0].[ParentId], [c0].[Slug], [c0].[SortOrder], [c0].[UpdatedAt]
FROM (
    SELECT TOP(@__p_0) [p].[Id], [p].[AverageRating], [p].[CategoryId], [p].[CreatedAt], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[IsFeatured], [p].[Name], [p].[OriginalPrice], [p].[Price], [p].[SKU], [p].[Slug], [p].[StockQuantity], [p].[UpdatedAt]
    FROM [Products] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[IsFeatured] = CAST(1 AS bit) AND [p].[IsActive] = CAST(1 AS bit) AND [p].[IsDeleted] = CAST(0 AS bit)
    ORDER BY [p].[CreatedAt] DESC
) AS [p0]
INNER JOIN (
    SELECT [c].[Id], [c].[CreatedAt], [c].[Description], [c].[IsDeleted], [c].[Name], [c].[ParentId], [c].[Slug], [c].[SortOrder], [c].[UpdatedAt]
    FROM [Categories] AS [c]
    WHERE [c].[IsDeleted] = CAST(0 AS bit)
) AS [c0] ON [p0].[CategoryId] = [c0].[Id]
ORDER BY [p0].[CreatedAt] DESC, [p0].[Id], [c0].[Id] {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.731 +07:00 ERR] Error during security events cleanup {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'SecurityEvents'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DecorStore.API.Services.TokenCleanupService.CleanupOldSecurityEventsAsync() in D:\Personal Projects\Decor\BE\DecorStore.API\Services\TokenCleanupService.cs:line 116
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16
[2025-06-17 11:11:54.733 +07:00 ERR] An exception occurred while iterating over the results of a query for context type 'DecorStore.API.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16 {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.743 +07:00 WRN] Failed to warm up featured products cache {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DecorStore.API.Repositories.ProductRepository.<>c__DisplayClass11_0.<<GetFeaturedProductsAsync>b__0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Repositories\ProductRepository.cs:line 112
--- End of stack trace from previous location ---
   at DecorStore.API.Services.CacheService.GetOrCreateAsync[T](String key, Func`1 factory, TimeSpan expiration, CacheItemPriority priority, String tag) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\CacheService.cs:line 109
   at DecorStore.API.Repositories.ProductRepository.GetFeaturedProductsAsync(Int32 count) in D:\Personal Projects\Decor\BE\DecorStore.API\Repositories\ProductRepository.cs:line 110
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.<>c__DisplayClass8_0.<<WarmupFeaturedProducts>b__0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 115
--- End of stack trace from previous location ---
   at DecorStore.API.Services.CacheService.GetOrCreateAsync[T](String key, Func`1 factory, Nullable`1 expiration) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\CacheService.cs:line 82
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.WarmupFeaturedProducts(CancellationToken cancellationToken) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 113
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.752 +07:00 ERR] Failed executing DbCommand (1ms) [Parameters=[@__p_1='?' (DbType = Int32), @__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_1) [s].[Id], [s].[Action], [s].[CorrelationId], [s].[CreatedAt], [s].[Details], [s].[ErrorCode], [s].[ErrorMessage], [s].[EventCategory], [s].[EventType], [s].[GeolocationCity], [s].[GeolocationCountry], [s].[HttpMethod], [s].[IpAddress], [s].[IsAnomaly], [s].[IsDeleted], [s].[IsProcessed], [s].[ProcessedAt], [s].[ProcessedBy], [s].[Recommendations], [s].[RequestPath], [s].[RequiresInvestigation], [s].[Resource], [s].[ResponseStatusCode], [s].[ResponseTimeMs], [s].[RiskScore], [s].[SessionId], [s].[Severity], [s].[Success], [s].[ThreatType], [s].[Timestamp], [s].[UpdatedAt], [s].[UserAgent], [s].[UserId], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[RequiresInvestigation] = CAST(1 AS bit) AND [s].[Timestamp] >= @__cutoffTime_0 AND [s].[IsProcessed] = CAST(0 AS bit)
ORDER BY [s].[RiskScore] DESC {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.756 +07:00 ERR] An exception occurred while iterating over the results of a query for context type 'DecorStore.API.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'SecurityEvents'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16 {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'SecurityEvents'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16
[2025-06-17 11:11:54.764 +07:00 ERR] Error checking security alerts {"SourceContext":"DecorStore.API.Services.SecurityEventLogger","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'SecurityEvents'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DecorStore.API.Services.SecurityEventLogger.CheckSecurityAlertsAsync() in D:\Personal Projects\Decor\BE\DecorStore.API\Services\SecurityEventLogger.cs:line 409
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:208,State:1,Class:16
[2025-06-17 11:11:54.774 +07:00 ERR] Failed executing DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p0].[Id], [p0].[AverageRating], [p0].[CategoryId], [p0].[CreatedAt], [p0].[Description], [p0].[IsActive], [p0].[IsDeleted], [p0].[IsFeatured], [p0].[Name], [p0].[OriginalPrice], [p0].[Price], [p0].[SKU], [p0].[Slug], [p0].[StockQuantity], [p0].[UpdatedAt], [c0].[Id], [c0].[CreatedAt], [c0].[Description], [c0].[IsDeleted], [c0].[Name], [c0].[ParentId], [c0].[Slug], [c0].[SortOrder], [c0].[UpdatedAt]
FROM (
    SELECT TOP(@__p_0) [p].[Id], [p].[AverageRating], [p].[CategoryId], [p].[CreatedAt], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[IsFeatured], [p].[Name], [p].[OriginalPrice], [p].[Price], [p].[SKU], [p].[Slug], [p].[StockQuantity], [p].[UpdatedAt]
    FROM [Products] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[IsActive] = CAST(1 AS bit) AND [p].[IsDeleted] = CAST(0 AS bit) AND [p].[AverageRating] > CAST(0 AS real)
    ORDER BY [p].[AverageRating] DESC, [p].[CreatedAt] DESC
) AS [p0]
INNER JOIN (
    SELECT [c].[Id], [c].[CreatedAt], [c].[Description], [c].[IsDeleted], [c].[Name], [c].[ParentId], [c].[Slug], [c].[SortOrder], [c].[UpdatedAt]
    FROM [Categories] AS [c]
    WHERE [c].[IsDeleted] = CAST(0 AS bit)
) AS [c0] ON [p0].[CategoryId] = [c0].[Id]
ORDER BY [p0].[AverageRating] DESC, [p0].[CreatedAt] DESC, [p0].[Id], [c0].[Id] {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.777 +07:00 ERR] An exception occurred while iterating over the results of a query for context type 'DecorStore.API.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16 {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.783 +07:00 WRN] Failed to warm up top-rated products cache {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DecorStore.API.Repositories.ProductRepository.<>c__DisplayClass14_0.<<GetTopRatedProductsAsync>b__0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Repositories\ProductRepository.cs:line 167
--- End of stack trace from previous location ---
   at DecorStore.API.Services.CacheService.GetOrCreateAsync[T](String key, Func`1 factory, TimeSpan expiration, CacheItemPriority priority, String tag) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\CacheService.cs:line 109
   at DecorStore.API.Repositories.ProductRepository.GetTopRatedProductsAsync(Int32 count) in D:\Personal Projects\Decor\BE\DecorStore.API\Repositories\ProductRepository.cs:line 165
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.<>c__DisplayClass9_0.<<WarmupTopRatedProducts>b__0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 136
--- End of stack trace from previous location ---
   at DecorStore.API.Services.CacheService.GetOrCreateAsync[T](String key, Func`1 factory, Nullable`1 expiration) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\CacheService.cs:line 82
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.WarmupTopRatedProducts(CancellationToken cancellationToken) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 134
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.967 +07:00 ERR] Failed executing DbCommand (1ms) [Parameters=[@__threshold_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[AverageRating], [p].[CategoryId], [p].[CreatedAt], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[IsFeatured], [p].[Name], [p].[OriginalPrice], [p].[Price], [p].[SKU], [p].[Slug], [p].[StockQuantity], [p].[UpdatedAt], [c0].[Id], [c0].[CreatedAt], [c0].[Description], [c0].[IsDeleted], [c0].[Name], [c0].[ParentId], [c0].[Slug], [c0].[SortOrder], [c0].[UpdatedAt]
FROM [Products] AS [p]
INNER JOIN (
    SELECT [c].[Id], [c].[CreatedAt], [c].[Description], [c].[IsDeleted], [c].[Name], [c].[ParentId], [c].[Slug], [c].[SortOrder], [c].[UpdatedAt]
    FROM [Categories] AS [c]
    WHERE [c].[IsDeleted] = CAST(0 AS bit)
) AS [c0] ON [p].[CategoryId] = [c0].[Id]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[StockQuantity] <= @__threshold_0 AND [p].[IsActive] = CAST(1 AS bit) AND [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[StockQuantity], [p].[Name], [p].[Id], [c0].[Id] {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:11:54.971 +07:00 ERR] An exception occurred while iterating over the results of a query for context type 'DecorStore.API.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16 {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.976 +07:00 WRN] Failed to warm up dashboard stats cache {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'SortOrder'.
Invalid column name 'UpdatedAt'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DecorStore.API.Repositories.ProductRepository.GetLowStockProductsAsync(Int32 threshold) in D:\Personal Projects\Decor\BE\DecorStore.API\Repositories\ProductRepository.cs:line 183
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.<>c__DisplayClass11_0.<<WarmupDashboardStats>b__0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 184
--- End of stack trace from previous location ---
   at DecorStore.API.Services.CacheService.GetOrCreateAsync[T](String key, Func`1 factory, Nullable`1 expiration) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\CacheService.cs:line 82
   at DecorStore.API.Services.BackgroundServices.CacheWarmupService.WarmupDashboardStats(CancellationToken cancellationToken) in D:\Personal Projects\Decor\BE\DecorStore.API\Services\BackgroundServices\CacheWarmupService.cs:line 180
ClientConnectionId:a8831ba3-f99a-41eb-8c59-5e2c7f09369f
Error Number:207,State:1,Class:16
[2025-06-17 11:11:54.981 +07:00 INF] Cache warmup completed in 1156.8703ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:17.532 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:17.560 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:17.563 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:17.565 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:17.568 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:17.570 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:17.573 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:30.590 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:30.617 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:30.621 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:30.623 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:30.625 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:30.628 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:30.631 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:45.680 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:45.709 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:45.712 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:45.715 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:45.718 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:45.721 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:45.723 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:59.672 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:12:59.696 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.036 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.040 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.042 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.044 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.047 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.049 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.052 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.396 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.399 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.409 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.505 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:13:00.702 +07:00 INF] Cache warmup completed in 1004.3248ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:14:57.111 +07:00 INF] HTTP Request [6569a47d-3d55-4ada-8510-e0f5fb979b40]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 286 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "123",
  "confirmPassword": "123",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TTR:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TTR","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:14:57.210 +07:00 INF] Performance [6569a47d-3d55-4ada-8510-e0f5fb979b40]: POST /api/Auth/register completed in 87.7784ms with status 400. {"CorrelationId":"6569a47d-3d55-4ada-8510-e0f5fb979b40","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":87.7784,"StartTime":"2025-06-17T04:14:57.1163203Z","EndTime":"2025-06-17T04:14:57.2040986Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":286} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TTR:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TTR","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:14:57.218 +07:00 WRN] HTTP Response [6569a47d-3d55-4ada-8510-e0f5fb979b40]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 595 | Duration: 110.1965ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["The field Password must be a string with a minimum length of 6 and a maximum length of 100.","Password must be at least 8 characters long","Password must contain at least one uppercase letter","Password must contain at least one lowercase letter","Password must contain at least one special character (!@#$%^&*)","Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-39f85aab1c0c3967fcc4a31a5d3fe7ad-44c472d9200ce9af-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TTR:00000004","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TTR","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:15:13.128 +07:00 INF] HTTP Request [0846c016-c707-4846-9a99-54bbd3754d04]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 300 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "Anhvip@522",
  "confirmPassword": "Anhvip@522",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TTR:00000005","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TTR","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:15:13.434 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"cea00755-997c-489a-bbca-7fa80ead6bf6","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDAM025TTR:00000005","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TTR","CorrelationId":"0846c016-c707-4846-9a99-54bbd3754d04","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:15:13.478 +07:00 INF] Performance [0846c016-c707-4846-9a99-54bbd3754d04]: POST /api/Auth/register completed in 346.3283ms with status 201. {"CorrelationId":"0846c016-c707-4846-9a99-54bbd3754d04","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":346.3283,"StartTime":"2025-06-17T04:15:13.1321086Z","EndTime":"2025-06-17T04:15:13.4784372Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":300} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TTR:00000005","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TTR","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:15:13.484 +07:00 INF] HTTP Response [0846c016-c707-4846-9a99-54bbd3754d04]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 426 | Duration: 356.171ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.uzQXlH6Fs9Q8oyPmIAXzrTdQK9xMzEZuHQ0I0e0kA9A","user":{"id":1,"username":"truongadmin","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TTR:00000005","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TTR","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:18:00.414 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:23:00.404 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:23:08.219 +07:00 INF] HTTP Request [87b72559-84e8-4bc1-86de-92523411d5d0]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 55 | Body: {
  "email": "{{adminEmail}}",
  "password": "123456"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TTU:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TTU","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:23:08.234 +07:00 INF] Performance [87b72559-84e8-4bc1-86de-92523411d5d0]: POST /api/Auth/login completed in 11.3537ms with status 400. {"CorrelationId":"87b72559-84e8-4bc1-86de-92523411d5d0","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":11.3537,"StartTime":"2025-06-17T04:23:08.2235373Z","EndTime":"2025-06-17T04:23:08.2348911Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":55} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TTU:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TTU","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:23:08.242 +07:00 WRN] HTTP Response [87b72559-84e8-4bc1-86de-92523411d5d0]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 252 | Duration: 23.2852ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Email":["Please provide a valid email address"]},"traceId":"00-8d15b06524e4a8ac9b973109978c9891-20d2db77ba627a6f-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TTU:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TTU","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:24:27.895 +07:00 INF] HTTP Request [16f32816-84f2-4dff-91e9-f51fd7ee6003]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 59 | Body: {
  "email": "{{adminEmail}}",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU0:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU0","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:24:27.901 +07:00 INF] Performance [16f32816-84f2-4dff-91e9-f51fd7ee6003]: POST /api/Auth/login completed in 1.2341ms with status 400. {"CorrelationId":"16f32816-84f2-4dff-91e9-f51fd7ee6003","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":1.2341,"StartTime":"2025-06-17T04:24:27.9005489Z","EndTime":"2025-06-17T04:24:27.9017837Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":59} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU0:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU0","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:24:27.911 +07:00 WRN] HTTP Response [16f32816-84f2-4dff-91e9-f51fd7ee6003]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 252 | Duration: 15.5078ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Email":["Please provide a valid email address"]},"traceId":"00-84829f5a5de9840f306003509fcee423-b40a2b7cf812588a-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU0:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU0","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:24:55.524 +07:00 INF] HTTP Request [9c0666e4-188a-4485-9b9d-2afd52a0b22d]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 66 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU0:00000002","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU0","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:24:55.723 +07:00 INF] Performance [9c0666e4-188a-4485-9b9d-2afd52a0b22d]: POST /api/Auth/login completed in 191.7618ms with status 200. {"CorrelationId":"9c0666e4-188a-4485-9b9d-2afd52a0b22d","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":191.7618,"StartTime":"2025-06-17T04:24:55.5320476Z","EndTime":"2025-06-17T04:24:55.7238097Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":66} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU0:00000002","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU0","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:24:55.733 +07:00 INF] HTTP Response [9c0666e4-188a-4485-9b9d-2afd52a0b22d]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 428 | Duration: 208.2568ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.5kpEYUO54z4kxRXBojFXgRWvgvRMw8svjTTslGpKIKM","user":{"id":1,"username":"truongadmin","email":"<EMAIL>","role":"Admin"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU0:00000002","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU0","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:26:40.383 +07:00 INF] HTTP Request [2e79782d-4b96-4ec1-9301-0b2440a7ef1e]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 45 | Body: {
  "email": "",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:26:40.391 +07:00 INF] Performance [2e79782d-4b96-4ec1-9301-0b2440a7ef1e]: POST /api/Auth/login completed in 1.4315ms with status 400. {"CorrelationId":"2e79782d-4b96-4ec1-9301-0b2440a7ef1e","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":1.4315,"StartTime":"2025-06-17T04:26:40.3895298Z","EndTime":"2025-06-17T04:26:40.3909615Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":45} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:26:40.401 +07:00 WRN] HTTP Response [2e79782d-4b96-4ec1-9301-0b2440a7ef1e]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 303 | Duration: 17.9907ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Email":["The Email field is required.","Email is required","Please provide a valid email address"]},"traceId":"00-387881c82804133196c1dc741f52de27-396b264c5144d195-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:27:38.592 +07:00 INF] HTTP Request [6400c936-01b9-4d4d-81be-b171bc104f5d]: POST /api/Auth/make-admin ?email=<EMAIL> | Content-Type: application/json | Content-Length: 42 | Body: {
    "email": "<EMAIL>"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000002","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:27:38.601 +07:00 INF] Performance [6400c936-01b9-4d4d-81be-b171bc104f5d]: POST /api/Auth/make-admin completed in 4.7205ms with status 401. {"CorrelationId":"6400c936-01b9-4d4d-81be-b171bc104f5d","Method":"POST","Path":"/api/Auth/make-admin","QueryString":"?email=<EMAIL>","StatusCode":401,"DurationMs":4.7205,"StartTime":"2025-06-17T04:27:38.5970487Z","EndTime":"2025-06-17T04:27:38.6017694Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":42} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000002","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:27:38.612 +07:00 WRN] HTTP Response [6400c936-01b9-4d4d-81be-b171bc104f5d]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 19.6883ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000002","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:27:50.034 +07:00 INF] HTTP Request [4ac205df-cacb-46d9-98a9-dcfc69b31ba1]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 66 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000003","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:27:50.158 +07:00 INF] Performance [4ac205df-cacb-46d9-98a9-dcfc69b31ba1]: POST /api/Auth/login completed in 118.4434ms with status 200. {"CorrelationId":"4ac205df-cacb-46d9-98a9-dcfc69b31ba1","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":118.4434,"StartTime":"2025-06-17T04:27:50.0395953Z","EndTime":"2025-06-17T04:27:50.1580386Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":66} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000003","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:27:50.168 +07:00 INF] HTTP Response [4ac205df-cacb-46d9-98a9-dcfc69b31ba1]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 428 | Duration: 133.9851ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.qeCoHc5oDuAlgyt5lR2inLZNOVjqFDF1jBOGw-ZaI6E","user":{"id":1,"username":"truongadmin","email":"<EMAIL>","role":"Admin"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU2:00000003","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU2","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:28:00.408 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.834 +07:00 INF] HTTP Request [1f0c4b01-5603-4062-8e6a-e99b645c0581]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 300 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "Anhvip@522",
  "confirmPassword": "Anhvip@522",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.847 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-7f5445bd175ba294b612af3b768a545c-dde28e266c2cf0d5-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"cea00755-997c-489a-bbca-7fa80ead6bf6","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDAM025TU4:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.857 +07:00 INF] Performance [1f0c4b01-5603-4062-8e6a-e99b645c0581]: POST /api/Auth/register completed in 18.1279ms with status 400. {"CorrelationId":"1f0c4b01-5603-4062-8e6a-e99b645c0581","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":18.1279,"StartTime":"2025-06-17T04:29:07.8396035Z","EndTime":"2025-06-17T04:29:07.8577312Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":300} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.867 +07:00 WRN] HTTP Response [1f0c4b01-5603-4062-8e6a-e99b645c0581]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 32.8751ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-7f5445bd175ba294b612af3b768a545c-dde28e266c2cf0d5-00","timestamp":"2025-06-17T04:29:07.852Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000001","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.911 +07:00 INF] HTTP Request [adf7af43-e35e-4794-8163-353e0706d238]: POST /api/Auth/make-admin ?email=<EMAIL> | Content-Type: application/json | Content-Length: 42 | Body: {
    "email": "<EMAIL>"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000002","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.917 +07:00 INF] Performance [adf7af43-e35e-4794-8163-353e0706d238]: POST /api/Auth/make-admin completed in 2.5273ms with status 401. {"CorrelationId":"adf7af43-e35e-4794-8163-353e0706d238","Method":"POST","Path":"/api/Auth/make-admin","QueryString":"?email=<EMAIL>","StatusCode":401,"DurationMs":2.5273,"StartTime":"2025-06-17T04:29:07.9152759Z","EndTime":"2025-06-17T04:29:07.9178035Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":42} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000002","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.926 +07:00 WRN] HTTP Response [adf7af43-e35e-4794-8163-353e0706d238]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 15.8201ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000002","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:07.943 +07:00 INF] HTTP Request [5cb341a8-4328-4d85-aef7-8bb6564a1644]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 66 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000003","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.063 +07:00 INF] Performance [5cb341a8-4328-4d85-aef7-8bb6564a1644]: POST /api/Auth/login completed in 114.5268ms with status 200. {"CorrelationId":"5cb341a8-4328-4d85-aef7-8bb6564a1644","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":114.5268,"StartTime":"2025-06-17T04:29:07.9487101Z","EndTime":"2025-06-17T04:29:08.0632370Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":66} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000003","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.072 +07:00 INF] HTTP Response [5cb341a8-4328-4d85-aef7-8bb6564a1644]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 428 | Duration: 128.6031ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.Y1D2onYPCCmf99ML_X1to6RZ2K0aojp9fBXCM9_bWAc","user":{"id":1,"username":"truongadmin","email":"<EMAIL>","role":"Admin"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000003","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.090 +07:00 INF] HTTP Request [654160e8-4ca9-4146-bd4c-e6be0a2bb77e]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 47 | Body: {
  "email": "",
  "password": "Password123!"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000004","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.096 +07:00 INF] Performance [654160e8-4ca9-4146-bd4c-e6be0a2bb77e]: POST /api/Auth/login completed in 1.1362ms with status 400. {"CorrelationId":"654160e8-4ca9-4146-bd4c-e6be0a2bb77e","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":1.1362,"StartTime":"2025-06-17T04:29:08.0955177Z","EndTime":"2025-06-17T04:29:08.0966538Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":47} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000004","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.105 +07:00 WRN] HTTP Response [654160e8-4ca9-4146-bd4c-e6be0a2bb77e]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 303 | Duration: 14.3373ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Email":["The Email field is required.","Email is required","Please provide a valid email address"]},"traceId":"00-02d8704d3390443db0bb195adafe94b0-82be3a53ab4aea26-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000004","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.124 +07:00 INF] HTTP Request [19e2f067-3c3e-4b60-9de7-f0172949930e]: GET /api/Auth/user  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000005","RequestPath":"/api/Auth/user","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.133 +07:00 INF] Performance [19e2f067-3c3e-4b60-9de7-f0172949930e]: GET /api/Auth/user completed in 4.6881ms with status 401. {"CorrelationId":"19e2f067-3c3e-4b60-9de7-f0172949930e","Method":"GET","Path":"/api/Auth/user","QueryString":"","StatusCode":401,"DurationMs":4.6881,"StartTime":"2025-06-17T04:29:08.1291409Z","EndTime":"2025-06-17T04:29:08.1338290Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000005","RequestPath":"/api/Auth/user","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:08.142 +07:00 WRN] HTTP Response [19e2f067-3c3e-4b60-9de7-f0172949930e]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 18.1737ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000005","RequestPath":"/api/Auth/user","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.163 +07:00 INF] HTTP Request [2ae7fe85-afcd-49c7-af53-68d491822728]: POST /api/Auth/register  | Content-Type: application/json | Content-Length: 300 | Body: {
  "username": "truongadmin",
  "email": "<EMAIL>",
  "password": "Anhvip@522",
  "confirmPassword": "Anhvip@522",
  "firstName": "truong",
  "lastName": "tran",
  "phone": "123456789",
  "dateOfBirth": "2000-06-17T03:13:42.607Z",
  "acceptTerms": true,
  "acceptPrivacyPolicy": true
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000006","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.288 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"cea00755-997c-489a-bbca-7fa80ead6bf6","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDAM025TU4:00000006","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","CorrelationId":"2ae7fe85-afcd-49c7-af53-68d491822728","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.299 +07:00 INF] Performance [2ae7fe85-afcd-49c7-af53-68d491822728]: POST /api/Auth/register completed in 131.5865ms with status 201. {"CorrelationId":"2ae7fe85-afcd-49c7-af53-68d491822728","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":131.5865,"StartTime":"2025-06-17T04:29:44.1678895Z","EndTime":"2025-06-17T04:29:44.2994759Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":300} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000006","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.314 +07:00 INF] HTTP Response [2ae7fe85-afcd-49c7-af53-68d491822728]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 426 | Duration: 150.8714ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.TthXD-7iPNRQSVhebTD5PZORQpwOh2SP0o9Lui8Sjv8","user":{"id":2,"username":"truongadmin","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000006","RequestPath":"/api/Auth/register","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.360 +07:00 INF] HTTP Request [f8dbaa5f-6154-49f6-870f-3101df8ea853]: POST /api/Auth/make-admin ?email=<EMAIL> | Content-Type: application/json | Content-Length: 42 | Body: {
    "email": "<EMAIL>"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000007","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.388 +07:00 INF] Performance [f8dbaa5f-6154-49f6-870f-3101df8ea853]: POST /api/Auth/make-admin completed in 21.599ms with status 403. {"CorrelationId":"f8dbaa5f-6154-49f6-870f-3101df8ea853","Method":"POST","Path":"/api/Auth/make-admin","QueryString":"?email=<EMAIL>","StatusCode":403,"DurationMs":21.599,"StartTime":"2025-06-17T04:29:44.3664504Z","EndTime":"2025-06-17T04:29:44.3880497Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":42} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000007","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.399 +07:00 WRN] HTTP Response [f8dbaa5f-6154-49f6-870f-3101df8ea853]: 403 | Content-Type: N/A | Content-Length: 0 | Duration: 39.3424ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000007","RequestPath":"/api/Auth/make-admin","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.418 +07:00 INF] HTTP Request [d2a4b850-ddd7-451a-a453-64257f07d081]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 66 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000008","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.539 +07:00 INF] Performance [d2a4b850-ddd7-451a-a453-64257f07d081]: POST /api/Auth/login completed in 115.6562ms with status 200. {"CorrelationId":"d2a4b850-ddd7-451a-a453-64257f07d081","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":115.6562,"StartTime":"2025-06-17T04:29:44.4238588Z","EndTime":"2025-06-17T04:29:44.5395148Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":66} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000008","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.548 +07:00 INF] HTTP Response [d2a4b850-ddd7-451a-a453-64257f07d081]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 426 | Duration: 129.9544ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.TthXD-7iPNRQSVhebTD5PZORQpwOh2SP0o9Lui8Sjv8","user":{"id":2,"username":"truongadmin","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000008","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.570 +07:00 INF] HTTP Request [9f35134e-1234-4433-ad5a-8599a74fbe9a]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 68 | Body: {
  "email": "<EMAIL>",
  "password": "Password123!"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000009","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.691 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-5bd5ee9fba2d0541ba64205cee58b50a-49001418b6c06be3-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"aa3b71f1-11d4-4b09-96d6-d0d14a9829bb","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDAM025TU4:00000009","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.696 +07:00 INF] Performance [9f35134e-1234-4433-ad5a-8599a74fbe9a]: POST /api/Auth/login completed in 120.5753ms with status 400. {"CorrelationId":"9f35134e-1234-4433-ad5a-8599a74fbe9a","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":120.5753,"StartTime":"2025-06-17T04:29:44.5761496Z","EndTime":"2025-06-17T04:29:44.6967246Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":68} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000009","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.707 +07:00 WRN] HTTP Response [9f35134e-1234-4433-ad5a-8599a74fbe9a]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 137.0909ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-5bd5ee9fba2d0541ba64205cee58b50a-49001418b6c06be3-00","timestamp":"2025-06-17T04:29:44.696Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:00000009","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.729 +07:00 INF] HTTP Request [a8375752-d09f-4699-8cb9-2b3c35fbbc10]: GET /api/Auth/user  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000A","RequestPath":"/api/Auth/user","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.751 +07:00 INF] Performance [a8375752-d09f-4699-8cb9-2b3c35fbbc10]: GET /api/Auth/user completed in 17.6946ms with status 200. {"CorrelationId":"a8375752-d09f-4699-8cb9-2b3c35fbbc10","Method":"GET","Path":"/api/Auth/user","QueryString":"","StatusCode":200,"DurationMs":17.6946,"StartTime":"2025-06-17T04:29:44.7337926Z","EndTime":"2025-06-17T04:29:44.7514872Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000A","RequestPath":"/api/Auth/user","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:29:44.759 +07:00 INF] HTTP Response [a8375752-d09f-4699-8cb9-2b3c35fbbc10]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 79 | Duration: 30.252ms | Body: {"id":2,"username":"truongadmin","email":"<EMAIL>","role":"User"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000A","RequestPath":"/api/Auth/user","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:30:39.677 +07:00 INF] HTTP Request [1a26a33d-2766-4b2b-b621-e51f57c9ffae]: GET /api/Category/all  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000B","RequestPath":"/api/Category/all","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:30:39.709 +07:00 INF] Performance [1a26a33d-2766-4b2b-b621-e51f57c9ffae]: GET /api/Category/all completed in 27.8413ms with status 200. {"CorrelationId":"1a26a33d-2766-4b2b-b621-e51f57c9ffae","Method":"GET","Path":"/api/Category/all","QueryString":"","StatusCode":200,"DurationMs":27.8413,"StartTime":"2025-06-17T04:30:39.6816349Z","EndTime":"2025-06-17T04:30:39.7094762Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000B","RequestPath":"/api/Category/all","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:30:39.717 +07:00 INF] HTTP Response [1a26a33d-2766-4b2b-b621-e51f57c9ffae]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 344 | Duration: 40.5747ms | Body: [{"id":1,"name":"Lamps","slug":"lamps","description":"Decorative Lamps","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]},{"id":2,"name":"Wall Decor","slug":"wall-decor","description":"Wall Decoration Items","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]}] {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000B","RequestPath":"/api/Category/all","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:30:49.306 +07:00 INF] HTTP Request [d3ca98cb-b654-496c-bae8-78bc7a3def69]: GET /api/Category ?page=1&pageSize=10 | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000C","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:30:49.320 +07:00 INF] Performance [d3ca98cb-b654-496c-bae8-78bc7a3def69]: GET /api/Category completed in 9.2595ms with status 200. {"CorrelationId":"d3ca98cb-b654-496c-bae8-78bc7a3def69","Method":"GET","Path":"/api/Category","QueryString":"?page=1&pageSize=10","StatusCode":200,"DurationMs":9.2595,"StartTime":"2025-06-17T04:30:49.3106911Z","EndTime":"2025-06-17T04:30:49.3199523Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000C","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:30:49.328 +07:00 INF] HTTP Response [d3ca98cb-b654-496c-bae8-78bc7a3def69]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 22.1186ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU4:0000000C","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:30:49.349 +07:00 ERR] Unhandled exception occurred [fe51ca4b1f374f8888f5657ad52cc177]: InvalidOperationException - 'VaryByQueryKeys' requires the response cache middleware.. Context: {"CorrelationId":"fe51ca4b1f374f8888f5657ad52cc177","ExceptionType":"InvalidOperationException","Message":"'VaryByQueryKeys' requires the response cache middleware.","StackTrace":"   at Microsoft.AspNetCore.Mvc.Filters.ResponseCacheFilterExecutor.Execute(FilterContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\PerformanceLoggingMiddleware.cs:line 31\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 38\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 51\r\n   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\CorrelationIdMiddleware.cs:line 29\r\n   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)\r\n   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ApiKeyRateLimitingMiddleware.cs:line 101\r\n   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Extensions\\SecurityExtensions.cs:line 161\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 99\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"truongadmin","UserAgent":"PostmanRuntime/7.44.0","IpAddress":"::1","RequestPath":"/api/Category","RequestMethod":"GET","QueryString":"?page=1&pageSize=10","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDDAM025TU4:0000000C","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU4","Application":"DecorStore.API","Environment":"Development"}
System.InvalidOperationException: 'VaryByQueryKeys' requires the response cache middleware.
   at Microsoft.AspNetCore.Mvc.Filters.ResponseCacheFilterExecutor.Execute(FilterContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 99
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 11:32:34.225 +07:00 INF] HTTP Request [74d4f790-6ab7-4647-b540-52dfe87f5266]: GET /api/Category ?page=1&pageSize=10 | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU6:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU6","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:34.232 +07:00 INF] Performance [74d4f790-6ab7-4647-b540-52dfe87f5266]: GET /api/Category completed in 3.6265ms with status 200. {"CorrelationId":"74d4f790-6ab7-4647-b540-52dfe87f5266","Method":"GET","Path":"/api/Category","QueryString":"?page=1&pageSize=10","StatusCode":200,"DurationMs":3.6265,"StartTime":"2025-06-17T04:32:34.2289251Z","EndTime":"2025-06-17T04:32:34.2325523Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDAM025TU6:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU6","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:34.240 +07:00 INF] HTTP Response [74d4f790-6ab7-4647-b540-52dfe87f5266]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 14.845ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDAM025TU6:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU6","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:34.244 +07:00 ERR] Unhandled exception occurred [691416d7f5f8413695daed072351d4b3]: InvalidOperationException - 'VaryByQueryKeys' requires the response cache middleware.. Context: {"CorrelationId":"691416d7f5f8413695daed072351d4b3","ExceptionType":"InvalidOperationException","Message":"'VaryByQueryKeys' requires the response cache middleware.","StackTrace":"   at Microsoft.AspNetCore.Mvc.Filters.ResponseCacheFilterExecutor.Execute(FilterContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\PerformanceLoggingMiddleware.cs:line 31\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 38\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 51\r\n   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\CorrelationIdMiddleware.cs:line 29\r\n   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)\r\n   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ApiKeyRateLimitingMiddleware.cs:line 101\r\n   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Extensions\\SecurityExtensions.cs:line 161\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 99\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"truongadmin","UserAgent":"PostmanRuntime/7.44.0","IpAddress":"::1","RequestPath":"/api/Category","RequestMethod":"GET","QueryString":"?page=1&pageSize=10","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDDAM025TU6:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDAM025TU6","Application":"DecorStore.API","Environment":"Development"}
System.InvalidOperationException: 'VaryByQueryKeys' requires the response cache middleware.
   at Microsoft.AspNetCore.Mvc.Filters.ResponseCacheFilterExecutor.Execute(FilterContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 99
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 11:32:36.989 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:48.729 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:48.755 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.127 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.132 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.135 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.139 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.142 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.145 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.149 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.509 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.513 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.538 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.655 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:49.860 +07:00 INF] Cache warmup completed in 1102.5379ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:52.863 +07:00 INF] HTTP Request [57d0a3e1-02e6-440a-b198-621b0099a07c]: GET /api/Category ?page=1&pageSize=10 | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDB0324528:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDB0324528","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:53.006 +07:00 INF] Performance [57d0a3e1-02e6-440a-b198-621b0099a07c]: GET /api/Category completed in 130.0176ms with status 200. {"CorrelationId":"57d0a3e1-02e6-440a-b198-621b0099a07c","Method":"GET","Path":"/api/Category","QueryString":"?page=1&pageSize=10","StatusCode":200,"DurationMs":130.0176,"StartTime":"2025-06-17T04:32:52.8695403Z","EndTime":"2025-06-17T04:32:52.9995582Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDB0324528:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDB0324528","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:53.016 +07:00 INF] HTTP Response [57d0a3e1-02e6-440a-b198-621b0099a07c]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 465 | Duration: 153.7624ms | Body: {"items":[{"id":1,"name":"Lamps","slug":"lamps","description":"Decorative Lamps","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]},{"id":2,"name":"Wall Decor","slug":"wall-decor","description":"Wall Decoration Items","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]}],"pagination":{"currentPage":1,"pageSize":10,"totalCount":2,"totalPages":1,"hasNext":false,"hasPrevious":false}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDB0324528:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDB0324528","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:59.703 +07:00 INF] HTTP Request [5e27c291-c51e-496a-9941-7d74b68f5a33]: GET /api/Category/1  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDB0324528:00000002","RequestPath":"/api/Category/1","ConnectionId":"0HNDDB0324528","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:59.726 +07:00 INF] Performance [5e27c291-c51e-496a-9941-7d74b68f5a33]: GET /api/Category/1 completed in 18.178ms with status 200. {"CorrelationId":"5e27c291-c51e-496a-9941-7d74b68f5a33","Method":"GET","Path":"/api/Category/1","QueryString":"","StatusCode":200,"DurationMs":18.178,"StartTime":"2025-06-17T04:32:59.7079684Z","EndTime":"2025-06-17T04:32:59.7261474Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDB0324528:00000002","RequestPath":"/api/Category/1","ConnectionId":"0HNDDB0324528","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:59.737 +07:00 INF] HTTP Response [5e27c291-c51e-496a-9941-7d74b68f5a33]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 33.5245ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDB0324528:00000002","RequestPath":"/api/Category/1","ConnectionId":"0HNDDB0324528","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 11:32:59.763 +07:00 ERR] Unhandled exception occurred [b6e925290eb948aba054d3a68343440d]: AsyncValidatorInvokedSynchronouslyException - Validator "ProductAvailabilityValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.. Context: {"CorrelationId":"b6e925290eb948aba054d3a68343440d","ExceptionType":"AsyncValidatorInvokedSynchronouslyException","Message":"Validator \"ProductAvailabilityValidator\" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.","StackTrace":"   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207\r\n   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153\r\n   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)\r\n   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\PerformanceLoggingMiddleware.cs:line 31\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 38\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 51\r\n   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\CorrelationIdMiddleware.cs:line 29\r\n   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)\r\n   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ApiKeyRateLimitingMiddleware.cs:line 101\r\n   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Extensions\\SecurityExtensions.cs:line 161\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 99\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"truongadmin","UserAgent":"PostmanRuntime/7.44.0","IpAddress":"::1","RequestPath":"/api/Category/1","RequestMethod":"GET","QueryString":"","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDDB0324528:00000002","RequestPath":"/api/Category/1","ConnectionId":"0HNDDB0324528","Application":"DecorStore.API","Environment":"Development"}
FluentValidation.AsyncValidatorInvokedSynchronouslyException: Validator "ProductAvailabilityValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.
   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207
   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153
   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45
   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63
   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 99
   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 11:33:50.271 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:12.833 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:12.860 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:13.571 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:13.575 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:13.578 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:13.581 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:13.584 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:13.587 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:13.590 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:14.059 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:14.062 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:14.143 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:14.268 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:20:14.465 +07:00 INF] Cache warmup completed in 1602.9038ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:25:14.077 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:30:14.067 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:35:14.075 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:40:14.072 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:45:14.074 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:50:14.071 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 13:55:14.070 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 14:00:14.079 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 14:05:14.068 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 14:10:14.075 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 14:15:14.079 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 14:20:14.073 +07:00 INF] Cache statistics before cleanup: {"TotalRequests":0,"CacheHits":0,"CacheMisses":0,"HitRatio":0,"TotalKeys":0,"KeysWithTags":0,"MemoryCacheSize":0} {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 14:20:14.079 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 14:25:14.073 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:21.789 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:21.816 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.181 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.184 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.187 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.190 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.193 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.196 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.198 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.560 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.563 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.575 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.677 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:04:22.890 +07:00 INF] Cache warmup completed in 1072.4234ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:09:22.571 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:14:22.577 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:19:22.573 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:24:22.576 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:16.807 +07:00 INF] HTTP Request [60887228-e9f3-4ebd-a7b0-e7c747a2a6ba]: GET /api/products  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GB:00000001","RequestPath":"/api/products","ConnectionId":"0HNDDF32EB8GB","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:17.667 +07:00 INF] Performance [60887228-e9f3-4ebd-a7b0-e7c747a2a6ba]: GET /api/products completed in 848.4437ms with status 200. {"CorrelationId":"60887228-e9f3-4ebd-a7b0-e7c747a2a6ba","Method":"GET","Path":"/api/products","QueryString":"","StatusCode":200,"DurationMs":848.4437,"StartTime":"2025-06-17T08:27:16.8126545Z","EndTime":"2025-06-17T08:27:17.6610981Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDF32EB8GB:00000001","RequestPath":"/api/products","ConnectionId":"0HNDDF32EB8GB","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:17.675 +07:00 INF] HTTP Response [60887228-e9f3-4ebd-a7b0-e7c747a2a6ba]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 809 | Duration: 869.7251ms | Body: {"items":[{"id":1,"name":"Decorative Lamp","slug":"decorative-lamp","description":"","price":49.99,"originalPrice":0.00,"stockQuantity":100,"sku":"LAMP001","categoryId":1,"categoryName":"Lamps","isFeatured":false,"isActive":true,"averageRating":0,"createdAt":"2024-03-07T00:00:00.000Z","updatedAt":"2024-03-07T00:00:00.000Z","images":[],"imageDetails":[]},{"id":2,"name":"Wall Clock","slug":"wall-clock","description":"","price":35.50,"originalPrice":0.00,"stockQuantity":50,"sku":"CLOCK001","categoryId":2,"categoryName":"Wall Decor","isFeatured":false,"isActive":true,"averageRating":0,"createdAt":"2024-03-07T00:00:00.000Z","updatedAt":"2024-03-07T00:00:00.000Z","images":[],"imageDetails":[]}],"pagination":{"currentPage":1,"pageSize":10,"totalCount":2,"totalPages":1,"hasNext":false,"hasPrevious":false}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GB:00000001","RequestPath":"/api/products","ConnectionId":"0HNDDF32EB8GB","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:30.268 +07:00 INF] HTTP Request [921332dd-0198-4f86-ac4b-58b06ccc6ae4]: GET /api/categories  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GC:00000001","RequestPath":"/api/categories","ConnectionId":"0HNDDF32EB8GC","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:30.277 +07:00 INF] Performance [921332dd-0198-4f86-ac4b-58b06ccc6ae4]: GET /api/categories completed in 1.6155ms with status 404. {"CorrelationId":"921332dd-0198-4f86-ac4b-58b06ccc6ae4","Method":"GET","Path":"/api/categories","QueryString":"","StatusCode":404,"DurationMs":1.6155,"StartTime":"2025-06-17T08:27:30.2745331Z","EndTime":"2025-06-17T08:27:30.2761489Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDF32EB8GC:00000001","RequestPath":"/api/categories","ConnectionId":"0HNDDF32EB8GC","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:30.288 +07:00 WRN] HTTP Response [921332dd-0198-4f86-ac4b-58b06ccc6ae4]: 404 | Content-Type: N/A | Content-Length: 0 | Duration: 20.1025ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GC:00000001","RequestPath":"/api/categories","ConnectionId":"0HNDDF32EB8GC","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:40.836 +07:00 INF] HTTP Request [59e7290d-ade3-4b2d-939c-7076051715b5]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 51 | Body: {"username": "truongadmin", "password": "admin123"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GD:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDF32EB8GD","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:40.872 +07:00 INF] Performance [59e7290d-ade3-4b2d-939c-7076051715b5]: POST /api/auth/login completed in 29.4021ms with status 400. {"CorrelationId":"59e7290d-ade3-4b2d-939c-7076051715b5","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":29.4021,"StartTime":"2025-06-17T08:27:40.8431577Z","EndTime":"2025-06-17T08:27:40.8725598Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":51} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDF32EB8GD:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDF32EB8GD","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:27:40.882 +07:00 WRN] HTTP Response [59e7290d-ade3-4b2d-939c-7076051715b5]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 371 | Duration: 47.0177ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"$":["JSON deserialization for type 'DecorStore.API.DTOs.LoginDTO' was missing required properties including: 'email'."],"loginDto":["The loginDto field is required."]},"traceId":"00-0672f8cc670c69229a60c6d621c322eb-7092dc5fb1223a4b-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GD:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDF32EB8GD","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:35.354 +07:00 INF] HTTP Request [4a17ba13-9ceb-4b4c-8bf1-0fb08666022b]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 58 | Body: {"email": "<EMAIL>", "password": "admin123"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GE:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDF32EB8GE","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:35.667 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-e881aae52f1c957bcbbc0c727ca424dd-aee32cd3e234794d-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"ad6d57dd-7b04-4006-b267-7f2edaf8c147","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDF32EB8GE:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDF32EB8GE","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:35.676 +07:00 INF] Performance [4a17ba13-9ceb-4b4c-8bf1-0fb08666022b]: POST /api/auth/login completed in 317.7352ms with status 400. {"CorrelationId":"4a17ba13-9ceb-4b4c-8bf1-0fb08666022b","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":317.7352,"StartTime":"2025-06-17T08:28:35.3582639Z","EndTime":"2025-06-17T08:28:35.6759991Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":58} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDF32EB8GE:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDF32EB8GE","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:35.683 +07:00 WRN] HTTP Response [4a17ba13-9ceb-4b4c-8bf1-0fb08666022b]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 329.6188ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-e881aae52f1c957bcbbc0c727ca424dd-aee32cd3e234794d-00","timestamp":"2025-06-17T08:28:35.671Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GE:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDF32EB8GE","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:47.264 +07:00 INF] HTTP Request [1ddc7618-d3d7-461c-af26-c1f850d29880]: GET /api/products/1  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GF:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDF32EB8GF","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:47.284 +07:00 INF] Performance [1ddc7618-d3d7-461c-af26-c1f850d29880]: GET /api/products/1 completed in 14.1218ms with status 200. {"CorrelationId":"1ddc7618-d3d7-461c-af26-c1f850d29880","Method":"GET","Path":"/api/products/1","QueryString":"","StatusCode":200,"DurationMs":14.1218,"StartTime":"2025-06-17T08:28:47.2701627Z","EndTime":"2025-06-17T08:28:47.2842872Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDF32EB8GF:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDF32EB8GF","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:47.297 +07:00 INF] HTTP Response [1ddc7618-d3d7-461c-af26-c1f850d29880]: 200 | Content-Type: N/A | Content-Length: 0 | Duration: 33.0802ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDF32EB8GF:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDF32EB8GF","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:28:47.349 +07:00 ERR] Unhandled exception occurred [57b12dcc605b4e3d945d13e1292998f7]: AsyncValidatorInvokedSynchronouslyException - Validator "ProductAvailabilityValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.. Context: {"CorrelationId":"57b12dcc605b4e3d945d13e1292998f7","ExceptionType":"AsyncValidatorInvokedSynchronouslyException","Message":"Validator \"ProductAvailabilityValidator\" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.","StackTrace":"   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207\r\n   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153\r\n   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63\r\n   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)\r\n   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)\r\n   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\PerformanceLoggingMiddleware.cs:line 31\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 38\r\n   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\RequestResponseLoggingMiddleware.cs:line 51\r\n   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\CorrelationIdMiddleware.cs:line 29\r\n   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)\r\n   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ApiKeyRateLimitingMiddleware.cs:line 101\r\n   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Extensions\\SecurityExtensions.cs:line 161\r\n--- End of stack trace from previous location ---\r\n   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 149\r\n   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\ResponseCompressionMiddleware.cs:line 99\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\\Personal Projects\\Decor\\BE\\DecorStore.API\\Middleware\\GlobalExceptionHandlerMiddleware.cs:line 32","UserId":"Anonymous","UserAgent":"curl/8.13.0","IpAddress":"::1","RequestPath":"/api/products/1","RequestMethod":"GET","QueryString":"","InnerException":null} {"SourceContext":"DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware","RequestId":"0HNDDF32EB8GF:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDF32EB8GF","Application":"DecorStore.API","Environment":"Development"}
FluentValidation.AsyncValidatorInvokedSynchronouslyException: Validator "ProductAvailabilityValidator" can't be used with ASP.NET automatic validation as it contains asynchronous rules. ASP.NET's validation pipeline is not asynchronous and can't invoke asynchronous rules. Remove the asynchronous rules in order for this validator to run.
   at FluentValidation.AbstractValidator`1.Validate(ValidationContext`1 context) in /_/src/FluentValidation/AbstractValidator.cs:line 207
   at FluentValidation.AbstractValidator`1.FluentValidation.IValidator.Validate(IValidationContext context) in /_/src/FluentValidation/AbstractValidator.cs:line 153
   at FluentValidation.AspNetCore.FluentValidationModelValidator.Validate(ModelValidationContext mvContext) in /_/src/FluentValidation.AspNetCore/FluentValidationModelValidatorProvider.cs:line 146
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>n__1(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container)
   at FluentValidation.AspNetCore.FluentValidationVisitor.<>c__DisplayClass2_0.<Validate>g__BaseValidate|0() in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 45
   at FluentValidation.AspNetCore.FluentValidationVisitor.ValidateInternal(ModelMetadata metadata, String key, Object model, Func`1 continuation) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 63
   at FluentValidation.AspNetCore.FluentValidationVisitor.Validate(ModelMetadata metadata, String key, Object model, Boolean alwaysValidateAtTopLevel, Object container) in /_/src/FluentValidation.AspNetCore/FluentValidationVisitor.cs:line 47
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at DecorStore.API.Middleware.PerformanceLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\PerformanceLoggingMiddleware.cs:line 31
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 38
   at DecorStore.API.Middleware.RequestResponseLoggingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\RequestResponseLoggingMiddleware.cs:line 51
   at DecorStore.API.Middleware.CorrelationIdMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\CorrelationIdMiddleware.cs:line 29
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at DecorStore.API.Middleware.ApiKeyRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ApiKeyRateLimitingMiddleware.cs:line 101
   at DecorStore.API.Extensions.SecurityExtensions.<>c.<<UseSecurityHeaders>b__2_0>d.MoveNext() in D:\Personal Projects\Decor\BE\DecorStore.API\Extensions\SecurityExtensions.cs:line 161
--- End of stack trace from previous location ---
   at DecorStore.API.Middleware.JsonOptimizationMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 149
   at DecorStore.API.Middleware.ResponseCachingMiddleware.InvokeAsync(HttpContext context) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\ResponseCompressionMiddleware.cs:line 99
   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at DecorStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\Personal Projects\Decor\BE\DecorStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 32
[2025-06-17 15:29:22.578 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:34:22.572 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:39:22.571 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:44:22.571 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:49:22.571 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 15:54:22.577 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.428 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.455 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.798 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.801 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.803 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.806 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.810 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.812 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:01.816 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:02.154 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:02.157 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:02.168 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:02.267 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:02:02.483 +07:00 INF] Cache warmup completed in 1025.431ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:10.464 +07:00 INF] HTTP Request [5f8bd2ce-9f7a-4133-ac72-eb78d707d478]: GET /api/products/1  | Content-Type: N/A | Content-Length: 2774 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14B:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDFNJ6F14B","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:10.564 +07:00 INF] Performance [5f8bd2ce-9f7a-4133-ac72-eb78d707d478]: GET /api/products/1 completed in 92.2518ms with status 200. {"CorrelationId":"5f8bd2ce-9f7a-4133-ac72-eb78d707d478","Method":"GET","Path":"/api/products/1","QueryString":"","StatusCode":200,"DurationMs":92.2518,"StartTime":"2025-06-17T09:05:10.4683036Z","EndTime":"2025-06-17T09:05:10.5605555Z","ContentLength":0,"UserAgent":"vscode-restclient","RemoteIpAddress":"127.0.0.1","RequestSize":2774} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14B:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDFNJ6F14B","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:10.570 +07:00 INF] HTTP Response [5f8bd2ce-9f7a-4133-ac72-eb78d707d478]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 345 | Duration: 107.4322ms | Body: {"id":1,"name":"Decorative Lamp","slug":"decorative-lamp","description":"","price":49.99,"originalPrice":0.00,"stockQuantity":100,"sku":"LAMP001","categoryId":1,"categoryName":"Lamps","isFeatured":false,"isActive":true,"averageRating":0,"createdAt":"2024-03-07T00:00:00.000Z","updatedAt":"2024-03-07T00:00:00.000Z","images":[],"imageDetails":[]} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14B:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDFNJ6F14B","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:20.680 +07:00 INF] HTTP Request [f2533884-f282-4fbf-87e0-5e31a67ada0a]: GET /api/Category  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14C:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDFNJ6F14C","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:20.749 +07:00 INF] Performance [f2533884-f282-4fbf-87e0-5e31a67ada0a]: GET /api/Category completed in 64.4955ms with status 200. {"CorrelationId":"f2533884-f282-4fbf-87e0-5e31a67ada0a","Method":"GET","Path":"/api/Category","QueryString":"","StatusCode":200,"DurationMs":64.4955,"StartTime":"2025-06-17T09:05:20.6841765Z","EndTime":"2025-06-17T09:05:20.7486720Z","ContentLength":0,"UserAgent":"vscode-restclient","RemoteIpAddress":"127.0.0.1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14C:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDFNJ6F14C","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:20.754 +07:00 INF] HTTP Response [f2533884-f282-4fbf-87e0-5e31a67ada0a]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 465 | Duration: 74.252ms | Body: {"items":[{"id":1,"name":"Lamps","slug":"lamps","description":"Decorative Lamps","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]},{"id":2,"name":"Wall Decor","slug":"wall-decor","description":"Wall Decoration Items","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]}],"pagination":{"currentPage":1,"pageSize":10,"totalCount":2,"totalPages":1,"hasNext":false,"hasPrevious":false}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14C:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDFNJ6F14C","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:31.382 +07:00 INF] HTTP Request [21cf3fa5-6f8c-4309-b323-1343fc8d269c]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 68 | Body: {
  "email": "<EMAIL>",
  "password": "Admin123!"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14D:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14D","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:31.612 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-c51d835b225383b67b85331ab3fe85fc-362c1832c8735d9a-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"6e97709d-ef07-4949-8e30-f0ed3357e7df","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFNJ6F14D:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14D","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:31.618 +07:00 INF] Performance [21cf3fa5-6f8c-4309-b323-1343fc8d269c]: POST /api/auth/login completed in 232.5296ms with status 400. {"CorrelationId":"21cf3fa5-6f8c-4309-b323-1343fc8d269c","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":232.5296,"StartTime":"2025-06-17T09:05:31.3862596Z","EndTime":"2025-06-17T09:05:31.6187894Z","ContentLength":0,"UserAgent":"vscode-restclient","RemoteIpAddress":"127.0.0.1","RequestSize":68} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14D:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14D","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:31.625 +07:00 WRN] HTTP Response [21cf3fa5-6f8c-4309-b323-1343fc8d269c]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 243.6263ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-c51d835b225383b67b85331ab3fe85fc-362c1832c8735d9a-00","timestamp":"2025-06-17T09:05:31.615Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14D:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14D","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:41.943 +07:00 INF] HTTP Request [848ab41d-0dd0-4538-b3a3-f94b55acb665]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 69 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14E:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14E","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:42.112 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-88bd6ad0ccbf04793589f6023e2f772a-771937a92f616706-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"6e97709d-ef07-4949-8e30-f0ed3357e7df","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFNJ6F14E:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14E","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:42.115 +07:00 INF] Performance [848ab41d-0dd0-4538-b3a3-f94b55acb665]: POST /api/auth/login completed in 169.272ms with status 400. {"CorrelationId":"848ab41d-0dd0-4538-b3a3-f94b55acb665","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":169.272,"StartTime":"2025-06-17T09:05:41.9462021Z","EndTime":"2025-06-17T09:05:42.1154741Z","ContentLength":0,"UserAgent":"vscode-restclient","RemoteIpAddress":"127.0.0.1","RequestSize":69} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14E:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14E","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:42.121 +07:00 WRN] HTTP Response [848ab41d-0dd0-4538-b3a3-f94b55acb665]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 178.071ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-88bd6ad0ccbf04793589f6023e2f772a-771937a92f616706-00","timestamp":"2025-06-17T09:05:42.115Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14E:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14E","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:48.270 +07:00 INF] HTTP Request [1cb4b8c9-006c-4181-ae2e-3e7996cfba51]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 69 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14F:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14F","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:48.385 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-acf95af8e5318db68aae048dbe2e0ae2-37d64391841c16dc-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"6e97709d-ef07-4949-8e30-f0ed3357e7df","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFNJ6F14F:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14F","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:48.389 +07:00 INF] Performance [1cb4b8c9-006c-4181-ae2e-3e7996cfba51]: POST /api/auth/login completed in 115.4548ms with status 400. {"CorrelationId":"1cb4b8c9-006c-4181-ae2e-3e7996cfba51","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":115.4548,"StartTime":"2025-06-17T09:05:48.2736194Z","EndTime":"2025-06-17T09:05:48.3890742Z","ContentLength":0,"UserAgent":"vscode-restclient","RemoteIpAddress":"127.0.0.1","RequestSize":69} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14F:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14F","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:05:48.394 +07:00 WRN] HTTP Response [1cb4b8c9-006c-4181-ae2e-3e7996cfba51]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 124.6244ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-acf95af8e5318db68aae048dbe2e0ae2-37d64391841c16dc-00","timestamp":"2025-06-17T09:05:48.388Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14F:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14F","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:06:47.258 +07:00 INF] HTTP Request [5f5a5bcc-799e-4f7f-be68-f8b83a0fe1ad]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 69 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14G:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14G","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:06:47.375 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-4b8ab05b77f963f845ebdc89b1179433-213f15aaa6005446-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"6e97709d-ef07-4949-8e30-f0ed3357e7df","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFNJ6F14G:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14G","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:06:47.378 +07:00 INF] Performance [5f5a5bcc-799e-4f7f-be68-f8b83a0fe1ad]: POST /api/auth/login completed in 117.2981ms with status 400. {"CorrelationId":"5f5a5bcc-799e-4f7f-be68-f8b83a0fe1ad","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":117.2981,"StartTime":"2025-06-17T09:06:47.2615079Z","EndTime":"2025-06-17T09:06:47.3788059Z","ContentLength":0,"UserAgent":"vscode-restclient","RemoteIpAddress":"127.0.0.1","RequestSize":69} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14G:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14G","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:06:47.385 +07:00 WRN] HTTP Response [5f5a5bcc-799e-4f7f-be68-f8b83a0fe1ad]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 126.4731ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-4b8ab05b77f963f845ebdc89b1179433-213f15aaa6005446-00","timestamp":"2025-06-17T09:06:47.378Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14G:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFNJ6F14G","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:07:02.161 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:07:17.306 +07:00 INF] HTTP Request [151320ac-7d7f-4a2a-848a-b97552848cbe]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 66 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14I:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14I","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:07:17.492 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-2ae86622856fbf72b64bc914cb40fb64-307011f00c1d95c0-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"6e97709d-ef07-4949-8e30-f0ed3357e7df","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFNJ6F14I:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14I","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:07:17.502 +07:00 INF] Performance [151320ac-7d7f-4a2a-848a-b97552848cbe]: POST /api/Auth/login completed in 188.0811ms with status 400. {"CorrelationId":"151320ac-7d7f-4a2a-848a-b97552848cbe","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":188.0811,"StartTime":"2025-06-17T09:07:17.3102740Z","EndTime":"2025-06-17T09:07:17.4983553Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.1","RemoteIpAddress":"::1","RequestSize":66} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14I:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14I","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:07:17.510 +07:00 WRN] HTTP Response [151320ac-7d7f-4a2a-848a-b97552848cbe]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 204.5207ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-2ae86622856fbf72b64bc914cb40fb64-307011f00c1d95c0-00","timestamp":"2025-06-17T09:07:17.497Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14I:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14I","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:08:37.813 +07:00 INF] HTTP Request [f679f880-fb04-4e12-a318-0035366a3774]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 66 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14K:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14K","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:08:37.932 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-f029d256f4b6b59cd4534f80d9a6fc68-9b1b08130d1734fc-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"6e97709d-ef07-4949-8e30-f0ed3357e7df","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFNJ6F14K:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14K","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:08:37.935 +07:00 INF] Performance [f679f880-fb04-4e12-a318-0035366a3774]: POST /api/Auth/login completed in 119.3738ms with status 400. {"CorrelationId":"f679f880-fb04-4e12-a318-0035366a3774","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":119.3738,"StartTime":"2025-06-17T09:08:37.8163999Z","EndTime":"2025-06-17T09:08:37.9357737Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.1","RemoteIpAddress":"::1","RequestSize":66} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFNJ6F14K:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14K","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:08:37.941 +07:00 WRN] HTTP Response [f679f880-fb04-4e12-a318-0035366a3774]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 128.4447ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-f029d256f4b6b59cd4534f80d9a6fc68-9b1b08130d1734fc-00","timestamp":"2025-06-17T09:08:37.935Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFNJ6F14K:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDFNJ6F14K","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:11:15.062 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.231 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.258 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.620 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.625 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.635 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.639 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.643 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.646 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:40.650 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:41.027 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:41.030 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:41.043 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:41.143 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:13:41.346 +07:00 INF] Cache warmup completed in 1086.4747ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:26.988 +07:00 INF] HTTP Request [50ad64dc-13e7-4fc1-8935-f110da73a848]: GET /api/products/1  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS5:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDFT3C6DS5","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:27.095 +07:00 INF] Performance [50ad64dc-13e7-4fc1-8935-f110da73a848]: GET /api/products/1 completed in 96.2689ms with status 200. {"CorrelationId":"50ad64dc-13e7-4fc1-8935-f110da73a848","Method":"GET","Path":"/api/products/1","QueryString":"","StatusCode":200,"DurationMs":96.2689,"StartTime":"2025-06-17T09:14:26.9931542Z","EndTime":"2025-06-17T09:14:27.0894235Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFT3C6DS5:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDFT3C6DS5","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:27.103 +07:00 INF] HTTP Response [50ad64dc-13e7-4fc1-8935-f110da73a848]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 345 | Duration: 115.4973ms | Body: {"id":1,"name":"Decorative Lamp","slug":"decorative-lamp","description":"","price":49.99,"originalPrice":0.00,"stockQuantity":100,"sku":"LAMP001","categoryId":1,"categoryName":"Lamps","isFeatured":false,"isActive":true,"averageRating":0,"createdAt":"2024-03-07T00:00:00.000Z","updatedAt":"2024-03-07T00:00:00.000Z","images":[],"imageDetails":[]} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS5:00000001","RequestPath":"/api/products/1","ConnectionId":"0HNDDFT3C6DS5","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:45.199 +07:00 INF] HTTP Request [d3931989-268f-4e18-8e4f-9abefe77dd1d]: GET /api/Category  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS6:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDFT3C6DS6","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:45.266 +07:00 INF] Performance [d3931989-268f-4e18-8e4f-9abefe77dd1d]: GET /api/Category completed in 63.8847ms with status 200. {"CorrelationId":"d3931989-268f-4e18-8e4f-9abefe77dd1d","Method":"GET","Path":"/api/Category","QueryString":"","StatusCode":200,"DurationMs":63.8847,"StartTime":"2025-06-17T09:14:45.2023926Z","EndTime":"2025-06-17T09:14:45.2662776Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFT3C6DS6:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDFT3C6DS6","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:45.273 +07:00 INF] HTTP Response [d3931989-268f-4e18-8e4f-9abefe77dd1d]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 465 | Duration: 74.5026ms | Body: {"items":[{"id":1,"name":"Lamps","slug":"lamps","description":"Decorative Lamps","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]},{"id":2,"name":"Wall Decor","slug":"wall-decor","description":"Wall Decoration Items","parentName":"","createdAt":"2024-03-07T00:00:00.000Z","subcategories":[],"imageDetails":[]}],"pagination":{"currentPage":1,"pageSize":10,"totalCount":2,"totalPages":1,"hasNext":false,"hasPrevious":false}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS6:00000001","RequestPath":"/api/Category","ConnectionId":"0HNDDFT3C6DS6","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:55.700 +07:00 INF] HTTP Request [2d36a8c3-8fb9-40f2-93a1-e67dc42e47ca]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 56 | Body: {"email":"<EMAIL>","password":"Admin123!"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS7:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS7","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:55.953 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-7a0693613b09be2c946e8fc096c582de-051cbae6a8a07a59-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"12526340-db5e-4da8-bf7d-9eb8c48e5aa5","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFT3C6DS7:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS7","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:55.960 +07:00 INF] Performance [2d36a8c3-8fb9-40f2-93a1-e67dc42e47ca]: POST /api/auth/login completed in 256.9015ms with status 400. {"CorrelationId":"2d36a8c3-8fb9-40f2-93a1-e67dc42e47ca","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":256.9015,"StartTime":"2025-06-17T09:14:55.7032530Z","EndTime":"2025-06-17T09:14:55.9601546Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":56} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFT3C6DS7:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS7","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:14:55.969 +07:00 WRN] HTTP Response [2d36a8c3-8fb9-40f2-93a1-e67dc42e47ca]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 270.6677ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-7a0693613b09be2c946e8fc096c582de-051cbae6a8a07a59-00","timestamp":"2025-06-17T09:14:55.957Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS7:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS7","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:15:35.602 +07:00 INF] HTTP Request [87fb6a76-1e93-47dc-855f-d55486e0e783]: POST /api/auth/login  | Content-Type: application/json | Content-Length: 56 | Body: {"email":"<EMAIL>","password":"Admin123!"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS8:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS8","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:15:35.773 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-025927bcd33b322c859ceb03722c2337-fcbe22ac0f6b025d-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"12526340-db5e-4da8-bf7d-9eb8c48e5aa5","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDFT3C6DS8:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS8","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:15:35.777 +07:00 INF] Performance [87fb6a76-1e93-47dc-855f-d55486e0e783]: POST /api/auth/login completed in 169.4872ms with status 400. {"CorrelationId":"87fb6a76-1e93-47dc-855f-d55486e0e783","Method":"POST","Path":"/api/auth/login","QueryString":"","StatusCode":400,"DurationMs":169.4872,"StartTime":"2025-06-17T09:15:35.6076794Z","EndTime":"2025-06-17T09:15:35.7771665Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":56} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFT3C6DS8:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS8","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:15:35.783 +07:00 WRN] HTTP Response [87fb6a76-1e93-47dc-855f-d55486e0e783]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 181.3941ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-025927bcd33b322c859ceb03722c2337-fcbe22ac0f6b025d-00","timestamp":"2025-06-17T09:15:35.776Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS8:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNDDFT3C6DS8","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:15:57.829 +07:00 INF] HTTP Request [1dac807d-b6f3-46b0-bf70-7da918287a8d]: POST /api/auth/register  | Content-Type: application/json | Content-Length: 78 | Body: {"email":"<EMAIL>","password":"TestPass123!","fullName":"Test User"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS9:00000001","RequestPath":"/api/auth/register","ConnectionId":"0HNDDFT3C6DS9","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:15:57.847 +07:00 INF] Performance [1dac807d-b6f3-46b0-bf70-7da918287a8d]: POST /api/auth/register completed in 12.9435ms with status 400. {"CorrelationId":"1dac807d-b6f3-46b0-bf70-7da918287a8d","Method":"POST","Path":"/api/auth/register","QueryString":"","StatusCode":400,"DurationMs":12.9435,"StartTime":"2025-06-17T09:15:57.8343362Z","EndTime":"2025-06-17T09:15:57.8472797Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":78} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFT3C6DS9:00000001","RequestPath":"/api/auth/register","ConnectionId":"0HNDDFT3C6DS9","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:15:57.854 +07:00 WRN] HTTP Response [1dac807d-b6f3-46b0-bf70-7da918287a8d]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 427 | Duration: 24.2225ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"$":["JSON deserialization for type 'DecorStore.API.DTOs.RegisterDTO' was missing required properties including: 'username', 'confirmPassword', 'firstName', 'lastName'."],"registerDto":["The registerDto field is required."]},"traceId":"00-c37c2230462b032b2787bae2be69715f-1c4ce9779201019b-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DS9:00000001","RequestPath":"/api/auth/register","ConnectionId":"0HNDDFT3C6DS9","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:16:06.918 +07:00 INF] HTTP Request [120be335-4601-4f08-ac6a-59734ab4830c]: GET /api/products  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DSA:00000001","RequestPath":"/api/products","ConnectionId":"0HNDDFT3C6DSA","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:16:06.966 +07:00 INF] Performance [120be335-4601-4f08-ac6a-59734ab4830c]: GET /api/products completed in 43.2357ms with status 200. {"CorrelationId":"120be335-4601-4f08-ac6a-59734ab4830c","Method":"GET","Path":"/api/products","QueryString":"","StatusCode":200,"DurationMs":43.2357,"StartTime":"2025-06-17T09:16:06.9236141Z","EndTime":"2025-06-17T09:16:06.9668500Z","ContentLength":0,"UserAgent":"curl/8.13.0","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFT3C6DSA:00000001","RequestPath":"/api/products","ConnectionId":"0HNDDFT3C6DSA","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:16:06.977 +07:00 INF] HTTP Response [120be335-4601-4f08-ac6a-59734ab4830c]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 809 | Duration: 59.2642ms | Body: {"items":[{"id":1,"name":"Decorative Lamp","slug":"decorative-lamp","description":"","price":49.99,"originalPrice":0.00,"stockQuantity":100,"sku":"LAMP001","categoryId":1,"categoryName":"Lamps","isFeatured":false,"isActive":true,"averageRating":0,"createdAt":"2024-03-07T00:00:00.000Z","updatedAt":"2024-03-07T00:00:00.000Z","images":[],"imageDetails":[]},{"id":2,"name":"Wall Clock","slug":"wall-clock","description":"","price":35.50,"originalPrice":0.00,"stockQuantity":50,"sku":"CLOCK001","categoryId":2,"categoryName":"Wall Decor","isFeatured":false,"isActive":true,"averageRating":0,"createdAt":"2024-03-07T00:00:00.000Z","updatedAt":"2024-03-07T00:00:00.000Z","images":[],"imageDetails":[]}],"pagination":{"currentPage":1,"pageSize":10,"totalCount":2,"totalPages":1,"hasNext":false,"hasPrevious":false}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DSA:00000001","RequestPath":"/api/products","ConnectionId":"0HNDDFT3C6DSA","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:18:41.038 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:23:41.038 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:24:35.239 +07:00 INF] HTTP Request [5e06da2e-78f3-4d0d-8760-0193f9d11ca4]: GET /  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DSB:00000001","RequestPath":"/","ConnectionId":"0HNDDFT3C6DSB","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:24:35.244 +07:00 INF] Performance [5e06da2e-78f3-4d0d-8760-0193f9d11ca4]: GET / completed in 0.2235ms with status 404. {"CorrelationId":"5e06da2e-78f3-4d0d-8760-0193f9d11ca4","Method":"GET","Path":"/","QueryString":"","StatusCode":404,"DurationMs":0.2235,"StartTime":"2025-06-17T09:24:35.2445089Z","EndTime":"2025-06-17T09:24:35.2447324Z","ContentLength":0,"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","RemoteIpAddress":"::1","RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDFT3C6DSB:00000001","RequestPath":"/","ConnectionId":"0HNDDFT3C6DSB","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:24:35.258 +07:00 WRN] HTTP Response [5e06da2e-78f3-4d0d-8760-0193f9d11ca4]: 404 | Content-Type: N/A | Content-Length: 0 | Duration: 19.5342ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDFT3C6DSB:00000001","RequestPath":"/","ConnectionId":"0HNDDFT3C6DSB","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:26:56.375 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:01.698 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:01.724 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.056 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.060 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.062 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.064 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.067 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.070 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.072 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.404 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.407 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.419 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.518 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:27:02.731 +07:00 INF] Cache warmup completed in 1005.0541ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:02.414 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.564 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.590 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.939 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AccountLockout'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.942 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'ApiKey'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.944 +07:00 WRN] Entity 'Category' has a global query filter defined and is the required end of a relationship with the entity 'CategoryImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.947 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordHistory'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.949 +07:00 WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.952 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:46.955 +07:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'TokenBlacklist'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information. {"EventId":{"Id":10622,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:47.308 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:47.313 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:47.333 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:47.436 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:32:47.630 +07:00 INF] Cache warmup completed in 1038.5793ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:33:47.166 +07:00 INF] HTTP Request [942bc16e-3565-4cde-927d-ae30c37cb461]: POST /api/Auth/login  | Content-Type: application/json | Content-Length: 66 | Body: {
  "email": "<EMAIL>",
  "password": "Anhvip@522"
} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDG80T7UCP:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDG80T7UCP","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:33:47.446 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-87be91f181b6f6365c05773192bf241b-944d085321af261c-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"184c6984-c969-4b08-a8c5-6ea645021a70","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDG80T7UCP:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDG80T7UCP","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:33:47.468 +07:00 INF] Performance [942bc16e-3565-4cde-927d-ae30c37cb461]: POST /api/Auth/login completed in 292.3722ms with status 400. {"CorrelationId":"942bc16e-3565-4cde-927d-ae30c37cb461","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":292.3722,"StartTime":"2025-06-17T09:33:47.1702496Z","EndTime":"2025-06-17T09:33:47.4626216Z","ContentLength":0,"UserAgent":"PostmanRuntime/7.44.1","RemoteIpAddress":"::1","RequestSize":66} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDG80T7UCP:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDG80T7UCP","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:33:47.476 +07:00 WRN] HTTP Response [942bc16e-3565-4cde-927d-ae30c37cb461]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 312.335ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-87be91f181b6f6365c05773192bf241b-944d085321af261c-00","timestamp":"2025-06-17T09:33:47.450Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDG80T7UCP:00000001","RequestPath":"/api/Auth/login","ConnectionId":"0HNDDG80T7UCP","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:37:47.335 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:42:47.331 +07:00 INF] Performance Metrics - Cache Hit Ratio: 0.00%, Total Requests: 0, Cache Size: 0KB {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 16:47:23.558 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
