[2025-06-17 17:19:20.589 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.592 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.594 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.595 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.596 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.599 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.631 +07:00 INF] Cache warmup completed in 38.9833ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.636 +07:00 INF] HTTP Request [b0195de8-a5de-4cb0-99c3-e6f7eacae33f]: PUT /api/Products/99999  | Content-Type: application/json; charset=utf-8 | Content-Length: 284 | Body: {"id":0,"name":"Updated Product Name","slug":"","description":"Updated description","price":199.99,"originalPrice":0,"stockQuantity":20,"sku":"","categoryId":1,"isFeatured":true,"isActive":true,"isDigital":false,"weight":null,"dimensions":null,"tags":null,"images":null,"imageIds":[]} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2H","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.639 +07:00 INF] Performance [b0195de8-a5de-4cb0-99c3-e6f7eacae33f]: PUT /api/Products/99999 completed in 1.9775ms with status 401. {"CorrelationId":"b0195de8-a5de-4cb0-99c3-e6f7eacae33f","Method":"PUT","Path":"/api/Products/99999","QueryString":"","StatusCode":401,"DurationMs":1.9775,"StartTime":"2025-06-17T10:19:20.6370361Z","EndTime":"2025-06-17T10:19:20.6390140Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":284} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH1LVSB2H","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.639 +07:00 WRN] HTTP Response [b0195de8-a5de-4cb0-99c3-e6f7eacae33f]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 2.5605ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2H","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.640 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:20.640 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
