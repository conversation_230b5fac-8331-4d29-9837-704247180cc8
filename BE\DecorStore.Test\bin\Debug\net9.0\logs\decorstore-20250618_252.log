[2025-06-18 10:24:02.442 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.449 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.450 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.451 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.452 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.455 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.498 +07:00 INF] HTTP Request [7146639b-1251-4297-9912-177a9c65fa54]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 76 | Body: {"email":"<EMAIL>","password":"Anhvip@522","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8R","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.499 +07:00 INF] Cache warmup completed in 50.2263ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.700 +07:00 INF] Performance [7146639b-1251-4297-9912-177a9c65fa54]: POST /api/Auth/login completed in 202.6296ms with status 200. {"CorrelationId":"7146639b-1251-4297-9912-177a9c65fa54","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":202.6296,"StartTime":"2025-06-18T03:24:02.4982521Z","EndTime":"2025-06-18T03:24:02.7008816Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":76} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDE2U8O0Q8R","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.701 +07:00 INF] HTTP Response [7146639b-1251-4297-9912-177a9c65fa54]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 430 | Duration: 203.0963ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.t0a4dmwuGU3YdfRCDVNHND5cn7ooKtggzqJQKHRJDv0","user":{"id":12,"username":"truongadmin","email":"<EMAIL>","role":"Admin"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8R","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.703 +07:00 INF] HTTP Request [8d84a1d9-23ad-4b90-bc84-cf5e8b506196]: GET /api/PerformanceDashboard/trends  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8T","RequestPath":"/api/PerformanceDashboard/trends","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.704 +07:00 INF] Performance [8d84a1d9-23ad-4b90-bc84-cf5e8b506196]: GET /api/PerformanceDashboard/trends completed in 1.3322ms with status 401. {"CorrelationId":"8d84a1d9-23ad-4b90-bc84-cf5e8b506196","Method":"GET","Path":"/api/PerformanceDashboard/trends","QueryString":"","StatusCode":401,"DurationMs":1.3322,"StartTime":"2025-06-18T03:24:02.7034805Z","EndTime":"2025-06-18T03:24:02.7048131Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDE2U8O0Q8T","RequestPath":"/api/PerformanceDashboard/trends","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.705 +07:00 WRN] HTTP Response [8d84a1d9-23ad-4b90-bc84-cf5e8b506196]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 1.7832ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDE2U8O0Q8T","RequestPath":"/api/PerformanceDashboard/trends","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.706 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-18 10:24:02.706 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
