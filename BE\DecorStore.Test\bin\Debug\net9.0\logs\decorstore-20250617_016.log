[2025-06-17 17:17:55.482 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.485 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.486 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.487 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.488 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.491 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.547 +07:00 INF] HTTP Request [4436b140-aa69-47ea-b16e-c170261150a3]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 273 | Body: {"username":"testuser","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:55.5203073+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK8","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.556 +07:00 INF] Cache warmup completed in 70.9021ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.557 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-12696847220bf318d56bda60ff66f703-5b74fd7da59ded66-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"3cea2fad-8670-448f-93d8-14fd0b8182e5","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AK8","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.557 +07:00 INF] Performance [4436b140-aa69-47ea-b16e-c170261150a3]: POST /api/Auth/register completed in 9.9103ms with status 400. {"CorrelationId":"4436b140-aa69-47ea-b16e-c170261150a3","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":9.9103,"StartTime":"2025-06-17T10:17:55.5480165Z","EndTime":"2025-06-17T10:17:55.5579268Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":273} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AK8","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.558 +07:00 WRN] HTTP Response [4436b140-aa69-47ea-b16e-c170261150a3]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 10.3721ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-12696847220bf318d56bda60ff66f703-5b74fd7da59ded66-00","timestamp":"2025-06-17T10:17:55.557Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK8","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.579 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.579 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
