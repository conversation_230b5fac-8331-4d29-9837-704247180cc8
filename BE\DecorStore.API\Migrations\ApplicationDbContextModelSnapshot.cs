﻿// <auto-generated />
using System;
using DecorStore.API.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DecorStore.API.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("DecorStore.API.Models.AccountLockout", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("FailedAttempts")
                        .HasColumnType("int");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LockoutEndTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("LockoutReason")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LockoutStartTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("UnlockedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UnlockedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AccountLockouts");
                });

            modelBuilder.Entity("DecorStore.API.Models.ApiKey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AllowedDomains")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("AllowedIpAddresses")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedByUserId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Environment")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("bit");

                    b.Property<string>("KeyHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("KeyPrefix")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUsedFromIp")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("Metadata")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<long>("RateLimitPerDay")
                        .HasColumnType("bigint");

                    b.Property<long>("RateLimitPerHour")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("RevokedByUserId")
                        .HasColumnType("int");

                    b.Property<string>("RevokedReason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Scopes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("UsageCount")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ApiKeys_CreatedAt");

                    b.HasIndex("CreatedByUserId")
                        .HasDatabaseName("IX_ApiKeys_CreatedByUserId");

                    b.HasIndex("Environment")
                        .HasDatabaseName("IX_ApiKeys_Environment");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_ApiKeys_ExpiresAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ApiKeys_IsActive");

                    b.HasIndex("IsRevoked")
                        .HasDatabaseName("IX_ApiKeys_IsRevoked");

                    b.HasIndex("KeyPrefix")
                        .IsUnique()
                        .HasDatabaseName("IX_ApiKeys_KeyPrefix");

                    b.HasIndex("RevokedByUserId");

                    b.HasIndex("IsActive", "IsRevoked", "ExpiresAt")
                        .HasDatabaseName("IX_ApiKeys_Active_Revoked_Expires");

                    b.ToTable("ApiKeys");
                });

            modelBuilder.Entity("DecorStore.API.Models.ApiKeyUsage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ApiKeyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("HttpMethod")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSuspicious")
                        .HasColumnType("bit");

                    b.Property<long>("RequestSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<long>("ResponseSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<int>("ResponseStatusCode")
                        .HasColumnType("int");

                    b.Property<long>("ResponseTimeMs")
                        .HasColumnType("bigint");

                    b.Property<decimal>("RiskScore")
                        .HasColumnType("decimal(3,2)");

                    b.Property<string>("SuspiciousReason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId")
                        .HasDatabaseName("IX_ApiKeyUsages_ApiKeyId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ApiKeyUsages_CreatedAt");

                    b.HasIndex("IpAddress")
                        .HasDatabaseName("IX_ApiKeyUsages_IpAddress");

                    b.HasIndex("IsSuspicious")
                        .HasDatabaseName("IX_ApiKeyUsages_IsSuspicious");

                    b.HasIndex("ResponseStatusCode")
                        .HasDatabaseName("IX_ApiKeyUsages_ResponseStatusCode");

                    b.HasIndex("ApiKeyId", "CreatedAt")
                        .HasDatabaseName("IX_ApiKeyUsages_ApiKey_CreatedAt");

                    b.HasIndex("IsSuccessful", "CreatedAt")
                        .HasDatabaseName("IX_ApiKeyUsages_Successful_CreatedAt");

                    b.ToTable("ApiKeyUsages");
                });

            modelBuilder.Entity("DecorStore.API.Models.Banner", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Link")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Banners");
                });

            modelBuilder.Entity("DecorStore.API.Models.Cart", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Carts_CreatedAt");

                    b.HasIndex("SessionId")
                        .HasDatabaseName("IX_Carts_SessionId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_Carts_UserId");

                    b.ToTable("Carts");
                });

            modelBuilder.Entity("DecorStore.API.Models.CartItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CartId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CartId")
                        .HasDatabaseName("IX_CartItems_CartId");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_CartItems_ProductId");

                    b.ToTable("CartItems");
                });

            modelBuilder.Entity("DecorStore.API.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_Categories_IsDeleted");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("IX_Categories_ParentId");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2024, 3, 7, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Decorative Lamps",
                            IsDeleted = false,
                            Name = "Lamps",
                            Slug = "lamps",
                            SortOrder = 0,
                            UpdatedAt = new DateTime(2025, 6, 17, 4, 12, 30, 531, DateTimeKind.Utc).AddTicks(9903)
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2024, 3, 7, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Wall Decoration Items",
                            IsDeleted = false,
                            Name = "Wall Decor",
                            Slug = "wall-decor",
                            SortOrder = 0,
                            UpdatedAt = new DateTime(2025, 6, 17, 4, 12, 30, 532, DateTimeKind.Utc).AddTicks(896)
                        });
                });

            modelBuilder.Entity("DecorStore.API.Models.CategoryImage", b =>
                {
                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<int>("ImageId")
                        .HasColumnType("int");

                    b.HasKey("CategoryId", "ImageId");

                    b.HasIndex("ImageId");

                    b.ToTable("CategoryImages");
                });

            modelBuilder.Entity("DecorStore.API.Models.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Customers_CreatedAt");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("DecorStore.API.Models.Image", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AltText")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("Images");
                });

            modelBuilder.Entity("DecorStore.API.Models.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CustomerId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ShippingAddress")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ShippingCity")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ShippingCountry")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ShippingPostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ShippingState")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Orders_CreatedAt");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("IX_Orders_CustomerId");

                    b.HasIndex("OrderDate")
                        .HasDatabaseName("IX_Orders_OrderDate");

                    b.HasIndex("OrderStatus")
                        .HasDatabaseName("IX_Orders_OrderStatus");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_Orders_UserId");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("IX_Orders_Deleted_CreatedAt");

                    b.HasIndex("OrderStatus", "IsDeleted", "OrderDate")
                        .HasDatabaseName("IX_Orders_Status_Deleted_Date");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("DecorStore.API.Models.OrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("OrderId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("IX_OrderItems_OrderId");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_OrderItems_ProductId");

                    b.ToTable("OrderItems");
                });

            modelBuilder.Entity("DecorStore.API.Models.PasswordHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("HashedPassword")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_PasswordHistory_CreatedAt");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_PasswordHistory_UserId");

                    b.HasIndex("UserId", "CreatedAt")
                        .HasDatabaseName("IX_PasswordHistory_User_CreatedAt");

                    b.ToTable("PasswordHistories");
                });

            modelBuilder.Entity("DecorStore.API.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<float>("AverageRating")
                        .HasColumnType("real");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal>("OriginalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SKU")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("StockQuantity")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AverageRating")
                        .HasDatabaseName("IX_Products_AverageRating");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("IX_Products_CategoryId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Products_CreatedAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Products_IsActive");

                    b.HasIndex("IsFeatured")
                        .HasDatabaseName("IX_Products_IsFeatured");

                    b.HasIndex("Price")
                        .HasDatabaseName("IX_Products_Price");

                    b.HasIndex("SKU")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("StockQuantity")
                        .HasDatabaseName("IX_Products_StockQuantity");

                    b.HasIndex("CategoryId", "IsActive", "IsDeleted")
                        .HasDatabaseName("IX_Products_Category_Active_Deleted");

                    b.HasIndex("IsActive", "IsFeatured", "IsDeleted")
                        .HasDatabaseName("IX_Products_Active_Featured_Deleted");

                    b.HasIndex("Price", "IsActive", "IsDeleted")
                        .HasDatabaseName("IX_Products_Price_Active_Deleted");

                    b.ToTable("Products");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AverageRating = 0f,
                            CategoryId = 1,
                            CreatedAt = new DateTime(2024, 3, 7, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsActive = true,
                            IsDeleted = false,
                            IsFeatured = false,
                            Name = "Decorative Lamp",
                            OriginalPrice = 0m,
                            Price = 49.99m,
                            SKU = "LAMP001",
                            Slug = "decorative-lamp",
                            StockQuantity = 100,
                            UpdatedAt = new DateTime(2024, 3, 7, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 2,
                            AverageRating = 0f,
                            CategoryId = 2,
                            CreatedAt = new DateTime(2024, 3, 7, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsActive = true,
                            IsDeleted = false,
                            IsFeatured = false,
                            Name = "Wall Clock",
                            OriginalPrice = 0m,
                            Price = 35.50m,
                            SKU = "CLOCK001",
                            Slug = "wall-clock",
                            StockQuantity = 50,
                            UpdatedAt = new DateTime(2024, 3, 7, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("DecorStore.API.Models.ProductImage", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("ImageId")
                        .HasColumnType("int");

                    b.HasKey("ProductId", "ImageId");

                    b.HasIndex("ImageId");

                    b.ToTable("ProductImages");
                });

            modelBuilder.Entity("DecorStore.API.Models.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByIp")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("bit");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("bit");

                    b.Property<string>("JwtId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ReplacedByToken")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("RevokedByIp")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RevokedReason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("TokenFamily")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("TokenVersion")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ExpiryDate")
                        .HasDatabaseName("IX_RefreshTokens_ExpiryDate");

                    b.HasIndex("Token")
                        .IsUnique()
                        .HasDatabaseName("IX_RefreshTokens_Token");

                    b.HasIndex("TokenFamily")
                        .HasDatabaseName("IX_RefreshTokens_TokenFamily");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_RefreshTokens_UserId");

                    b.HasIndex("IsUsed", "IsRevoked", "ExpiryDate")
                        .HasDatabaseName("IX_RefreshTokens_Status_Expiry");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("DecorStore.API.Models.Review", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("Rating")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Reviews_CreatedAt");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_Reviews_ProductId");

                    b.HasIndex("Rating")
                        .HasDatabaseName("IX_Reviews_Rating");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_Reviews_UserId");

                    b.ToTable("Reviews");
                });

            modelBuilder.Entity("DecorStore.API.Models.SecurityEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ErrorCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("EventCategory")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("GeolocationCity")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("GeolocationCountry")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("HttpMethod")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<bool>("IsAnomaly")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsProcessed")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ProcessedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Recommendations")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("RequestPath")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("RequiresInvestigation")
                        .HasColumnType("bit");

                    b.Property<string>("Resource")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("ResponseStatusCode")
                        .HasColumnType("int");

                    b.Property<long?>("ResponseTimeMs")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("RiskScore")
                        .HasColumnType("decimal(3,2)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("Success")
                        .HasColumnType("bit");

                    b.Property<string>("ThreatType")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("Username")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("EventType")
                        .HasDatabaseName("IX_SecurityEvents_EventType");

                    b.HasIndex("IpAddress")
                        .HasDatabaseName("IX_SecurityEvents_IpAddress");

                    b.HasIndex("RequiresInvestigation")
                        .HasDatabaseName("IX_SecurityEvents_RequiresInvestigation");

                    b.HasIndex("Severity")
                        .HasDatabaseName("IX_SecurityEvents_Severity");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_SecurityEvents_Timestamp");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_SecurityEvents_UserId");

                    b.HasIndex("EventType", "Timestamp")
                        .HasDatabaseName("IX_SecurityEvents_Type_Timestamp");

                    b.HasIndex("UserId", "Timestamp")
                        .HasDatabaseName("IX_SecurityEvents_User_Timestamp");

                    b.HasIndex("Success", "Severity", "Timestamp")
                        .HasDatabaseName("IX_SecurityEvents_Success_Severity_Timestamp");

                    b.ToTable("SecurityEvents");
                });

            modelBuilder.Entity("DecorStore.API.Models.TokenBlacklist", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInfo")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("AutoRemovalDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("BlacklistReason")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("BlacklistType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("BlacklistedByIp")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BlacklistedByUserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRevocationPermanent")
                        .HasColumnType("bit");

                    b.Property<string>("JwtId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("TokenHash")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlacklistType")
                        .HasDatabaseName("IX_TokenBlacklist_BlacklistType");

                    b.HasIndex("ExpiryDate")
                        .HasDatabaseName("IX_TokenBlacklist_ExpiryDate");

                    b.HasIndex("JwtId")
                        .HasDatabaseName("IX_TokenBlacklist_JwtId");

                    b.HasIndex("TokenHash")
                        .HasDatabaseName("IX_TokenBlacklist_TokenHash");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_TokenBlacklist_UserId");

                    b.ToTable("TokenBlacklists");
                });

            modelBuilder.Entity("DecorStore.API.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LockoutEnd")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("PasswordChangedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Users_CreatedAt");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("Role")
                        .HasDatabaseName("IX_Users_Role");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("DecorStore.API.Models.AccountLockout", b =>
                {
                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.ApiKey", b =>
                {
                    b.HasOne("DecorStore.API.Models.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DecorStore.API.Models.User", "RevokedByUser")
                        .WithMany()
                        .HasForeignKey("RevokedByUserId");

                    b.Navigation("CreatedByUser");

                    b.Navigation("RevokedByUser");
                });

            modelBuilder.Entity("DecorStore.API.Models.ApiKeyUsage", b =>
                {
                    b.HasOne("DecorStore.API.Models.ApiKey", "ApiKey")
                        .WithMany("UsageHistory")
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");
                });

            modelBuilder.Entity("DecorStore.API.Models.Cart", b =>
                {
                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.CartItem", b =>
                {
                    b.HasOne("DecorStore.API.Models.Cart", "Cart")
                        .WithMany("Items")
                        .HasForeignKey("CartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DecorStore.API.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Cart");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("DecorStore.API.Models.Category", b =>
                {
                    b.HasOne("DecorStore.API.Models.Category", "ParentCategory")
                        .WithMany("Subcategories")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("DecorStore.API.Models.CategoryImage", b =>
                {
                    b.HasOne("DecorStore.API.Models.Category", "Category")
                        .WithMany("CategoryImages")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DecorStore.API.Models.Image", "Image")
                        .WithMany("CategoryImages")
                        .HasForeignKey("ImageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Image");
                });

            modelBuilder.Entity("DecorStore.API.Models.Order", b =>
                {
                    b.HasOne("DecorStore.API.Models.Customer", "Customer")
                        .WithMany("Orders")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany("Orders")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.OrderItem", b =>
                {
                    b.HasOne("DecorStore.API.Models.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DecorStore.API.Models.Product", "Product")
                        .WithMany("OrderItems")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("DecorStore.API.Models.PasswordHistory", b =>
                {
                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany("PasswordHistory")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.Product", b =>
                {
                    b.HasOne("DecorStore.API.Models.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("DecorStore.API.Models.ProductImage", b =>
                {
                    b.HasOne("DecorStore.API.Models.Image", "Image")
                        .WithMany("ProductImages")
                        .HasForeignKey("ImageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DecorStore.API.Models.Product", "Product")
                        .WithMany("ProductImages")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Image");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("DecorStore.API.Models.RefreshToken", b =>
                {
                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.Review", b =>
                {
                    b.HasOne("DecorStore.API.Models.Product", "Product")
                        .WithMany("Reviews")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany("Reviews")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.SecurityEvent", b =>
                {
                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany("SecurityEvents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.TokenBlacklist", b =>
                {
                    b.HasOne("DecorStore.API.Models.User", "User")
                        .WithMany("BlacklistedTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("DecorStore.API.Models.ApiKey", b =>
                {
                    b.Navigation("UsageHistory");
                });

            modelBuilder.Entity("DecorStore.API.Models.Cart", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("DecorStore.API.Models.Category", b =>
                {
                    b.Navigation("CategoryImages");

                    b.Navigation("Products");

                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("DecorStore.API.Models.Customer", b =>
                {
                    b.Navigation("Orders");
                });

            modelBuilder.Entity("DecorStore.API.Models.Image", b =>
                {
                    b.Navigation("CategoryImages");

                    b.Navigation("ProductImages");
                });

            modelBuilder.Entity("DecorStore.API.Models.Order", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("DecorStore.API.Models.Product", b =>
                {
                    b.Navigation("OrderItems");

                    b.Navigation("ProductImages");

                    b.Navigation("Reviews");
                });

            modelBuilder.Entity("DecorStore.API.Models.User", b =>
                {
                    b.Navigation("BlacklistedTokens");

                    b.Navigation("Orders");

                    b.Navigation("PasswordHistory");

                    b.Navigation("RefreshTokens");

                    b.Navigation("Reviews");

                    b.Navigation("SecurityEvents");
                });
#pragma warning restore 612, 618
        }
    }
}
