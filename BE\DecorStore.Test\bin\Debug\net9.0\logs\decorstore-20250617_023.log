[2025-06-17 17:19:19.892 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.895 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.899 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.899 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.902 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.905 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.919 +07:00 INF] Cache warmup completed in 23.9322ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.936 +07:00 INF] HTTP Request [f7493f35-29c7-48fc-b637-5898de33af85]: DELETE /api/Products/99999  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2D","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.942 +07:00 INF] Performance [f7493f35-29c7-48fc-b637-5898de33af85]: DELETE /api/Products/99999 completed in 5.9175ms with status 401. {"CorrelationId":"f7493f35-29c7-48fc-b637-5898de33af85","Method":"DELETE","Path":"/api/Products/99999","QueryString":"","StatusCode":401,"DurationMs":5.9175,"StartTime":"2025-06-17T10:19:19.9365680Z","EndTime":"2025-06-17T10:19:19.9424861Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH1LVSB2D","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.942 +07:00 WRN] HTTP Response [f7493f35-29c7-48fc-b637-5898de33af85]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 6.4059ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH1LVSB2D","RequestPath":"/api/Products/99999","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.945 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:19:19.945 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
