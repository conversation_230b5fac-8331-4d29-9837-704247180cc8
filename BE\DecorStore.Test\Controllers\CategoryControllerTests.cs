using System.Net;
using System.Net.Http.Json;
using DecorStore.API.DTOs;
using FluentAssertions;
using Xunit;

namespace DecorStore.Test.Controllers
{
    public class CategoryControllerTests : TestBase
    {
        [Fact]
        public async Task GetCategories_ShouldReturnCategoryList()
        {
            // Arrange
            await SeedTestDataAsync();

            // Act
            var response = await _client.GetAsync("/api/Category");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var categories = await DeserializeResponseAsync<List<CategoryDTO>>(response);
            categories.Should().NotBeNull();
            categories!.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task GetCategory_WithValidId_ShouldReturnCategory()
        {
            // Arrange
            await SeedTestDataAsync();
            var allCategories = await _client.GetFromJsonAsync<List<CategoryDTO>>("/api/Category");
            var categoryId = allCategories!.First().Id;

            // Act
            var response = await _client.GetAsync($"/api/Category/{categoryId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var category = await DeserializeResponseAsync<CategoryDTO>(response);
            category.Should().NotBeNull();
            category!.Id.Should().Be(categoryId);
        }

        [Fact]
        public async Task GetCategory_WithInvalidId_ShouldReturnNotFound()
        {
            // Act
            var response = await _client.GetAsync("/api/Category/99999");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task CreateCategory_WithValidData_ShouldReturnCreated()
        {
            // Arrange
            var createCategoryDto = new CreateCategoryDTO
            {
                Name = "Test Category",
                Description = "A test category description",
                Slug = "test-category"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/Category", createCategoryDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var category = await DeserializeResponseAsync<CategoryDTO>(response);
            category.Should().NotBeNull();
            category!.Name.Should().Be(createCategoryDto.Name);
            category.Slug.Should().Be(createCategoryDto.Slug);
        }

        [Fact]
        public async Task CreateCategory_WithInvalidData_ShouldReturnBadRequest()
        {
            // Arrange
            var createCategoryDto = new CreateCategoryDTO
            {
                Name = "", // Invalid: empty name
                Description = "A test category description",
                Slug = "" // Invalid: empty slug
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/Category", createCategoryDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task UpdateCategory_WithValidData_ShouldReturnOk()
        {
            // Arrange
            await SeedTestDataAsync();
            var allCategories = await _client.GetFromJsonAsync<List<CategoryDTO>>("/api/Category");
            var category = allCategories!.First();

            var updateCategoryDto = new UpdateCategoryDTO
            {
                Name = "Updated Category Name",
                Description = "Updated description",
                Slug = "updated-category"
            };

            // Act
            var response = await _client.PutAsJsonAsync($"/api/Category/{category.Id}", updateCategoryDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var updatedCategory = await DeserializeResponseAsync<CategoryDTO>(response);
            updatedCategory.Should().NotBeNull();
            updatedCategory!.Name.Should().Be(updateCategoryDto.Name);
            updatedCategory.Slug.Should().Be(updateCategoryDto.Slug);
        }

        [Fact]
        public async Task UpdateCategory_WithInvalidId_ShouldReturnNotFound()
        {
            // Arrange
            var updateCategoryDto = new UpdateCategoryDTO
            {
                Name = "Updated Category Name",
                Description = "Updated description",
                Slug = "updated-category"
            };

            // Act
            var response = await _client.PutAsJsonAsync("/api/Category/99999", updateCategoryDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task DeleteCategory_WithValidId_ShouldReturnNoContent()
        {
            // Arrange
            await SeedTestDataAsync();
            var allCategories = await _client.GetFromJsonAsync<List<CategoryDTO>>("/api/Category");
            var categoryId = allCategories!.First().Id;

            // Act
            var response = await _client.DeleteAsync($"/api/Category/{categoryId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NoContent);
            
            // Verify category is deleted
            var getResponse = await _client.GetAsync($"/api/Category/{categoryId}");
            getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task DeleteCategory_WithInvalidId_ShouldReturnNotFound()
        {
            // Act
            var response = await _client.DeleteAsync("/api/Category/99999");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task GetCategoryBySlug_WithValidSlug_ShouldReturnCategory()
        {
            // Arrange
            await SeedTestDataAsync();
            var allCategories = await _client.GetFromJsonAsync<List<CategoryDTO>>("/api/Category");
            var category = allCategories!.First();

            // Act
            var response = await _client.GetAsync($"/api/Category/slug/{category.Slug}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var foundCategory = await DeserializeResponseAsync<CategoryDTO>(response);
            foundCategory.Should().NotBeNull();
            foundCategory!.Id.Should().Be(category.Id);
            foundCategory.Slug.Should().Be(category.Slug);
        }

        [Fact]
        public async Task GetCategoryBySlug_WithInvalidSlug_ShouldReturnNotFound()
        {
            // Act
            var response = await _client.GetAsync("/api/Category/slug/non-existent-slug");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task GetCategoryProducts_ShouldReturnProductsInCategory()
        {
            // Arrange
            await SeedTestDataAsync();
            var allCategories = await _client.GetFromJsonAsync<List<CategoryDTO>>("/api/Category");
            var categoryId = allCategories!.First().Id;

            // Act
            var response = await _client.GetAsync($"/api/Category/{categoryId}/products");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var products = await DeserializeResponseAsync<List<ProductDTO>>(response);
            products.Should().NotBeNull();
            products!.Should().OnlyContain(p => p.CategoryId == categoryId);
        }
    }
}
