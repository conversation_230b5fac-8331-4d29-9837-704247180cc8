[2025-06-17 17:15:30.263 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.266 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.267 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.267 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.269 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.271 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.286 +07:00 INF] Cache warmup completed in 20.2262ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.286 +07:00 INF] HTTP Request [9c9cb30a-26dd-4cfa-b09d-7092e04beece]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 275 | Body: {"username":"login","email":"<EMAIL>","password":"TestPassword123!","confirmPassword":"TestPassword123!","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.2804958+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHI5","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.291 +07:00 INF] Performance [9c9cb30a-26dd-4cfa-b09d-7092e04beece]: POST /api/Auth/register completed in 4.8725ms with status 400. {"CorrelationId":"9c9cb30a-26dd-4cfa-b09d-7092e04beece","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":4.8725,"StartTime":"2025-06-17T10:15:30.2868933Z","EndTime":"2025-06-17T10:15:30.2917658Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":275} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHI5","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.292 +07:00 WRN] HTTP Response [9c9cb30a-26dd-4cfa-b09d-7092e04beece]: 400 | Content-Type: application/problem+json; charset=utf-8 | Content-Length: 281 | Duration: 5.2639ms | Body: {"type":"https://tools.ietf.org/html/rfc9110#section-15.5.1","title":"One or more validation errors occurred.","status":400,"errors":{"Password":["Password cannot contain sequential characters (e.g., 123, abc)"]},"traceId":"00-19f0c92f16bf1b29c214c359834d332a-3a6421b07f54a9bb-00"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHI5","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.295 +07:00 INF] HTTP Request [044869e5-d987-45c1-ac6d-2af6853c1236]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 78 | Body: {"email":"<EMAIL>","password":"TestPassword123!","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHI7","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.299 +07:00 WRN] Request failed with error: Invalid email or password, ErrorCode: INVALID_CREDENTIALS, CorrelationId: 00-f784bd491830d05ea2bd4f838c2f5ec1-181078a0c1a53325-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"13ea0ec8-b42e-4594-a914-3d0725987764","ActionName":"DecorStore.API.Controllers.AuthController.Login (DecorStore.API)","RequestId":"0HNDDGVHETHI7","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.300 +07:00 INF] Performance [044869e5-d987-45c1-ac6d-2af6853c1236]: POST /api/Auth/login completed in 4.5983ms with status 400. {"CorrelationId":"044869e5-d987-45c1-ac6d-2af6853c1236","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":400,"DurationMs":4.5983,"StartTime":"2025-06-17T10:15:30.2953934Z","EndTime":"2025-06-17T10:15:30.2999916Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":78} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHI7","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.300 +07:00 WRN] HTTP Response [044869e5-d987-45c1-ac6d-2af6853c1236]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 184 | Duration: 5.3016ms | Body: {"error":"Invalid email or password","errorCode":"INVALID_CREDENTIALS","correlationId":"00-f784bd491830d05ea2bd4f838c2f5ec1-181078a0c1a53325-00","timestamp":"2025-06-17T10:15:30.299Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHI7","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.303 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.303 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
