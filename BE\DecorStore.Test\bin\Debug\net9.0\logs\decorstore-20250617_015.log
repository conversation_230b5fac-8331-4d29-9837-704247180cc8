[2025-06-17 17:17:54.939 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.943 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.945 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.945 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.946 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.949 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.965 +07:00 INF] Cache warmup completed in 21.3367ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.965 +07:00 INF] HTTP Request [c4b308c0-4e6c-4673-8c8d-ce8a071bd527]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 271 | Body: {"username":"login","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:17:54.9586838+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK4","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.248 +07:00 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"8a3748a5-5cfd-4d3a-a348-36decf60f2eb","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDH0SJ5AK4","RequestPath":"/api/Auth/register","CorrelationId":"c4b308c0-4e6c-4673-8c8d-ce8a071bd527","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.287 +07:00 INF] Performance [c4b308c0-4e6c-4673-8c8d-ce8a071bd527]: POST /api/Auth/register completed in 321.4596ms with status 201. {"CorrelationId":"c4b308c0-4e6c-4673-8c8d-ce8a071bd527","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":201,"DurationMs":321.4596,"StartTime":"2025-06-17T10:17:54.9656106Z","EndTime":"2025-06-17T10:17:55.2870708Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":271} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AK4","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.287 +07:00 INF] HTTP Response [c4b308c0-4e6c-4673-8c8d-ce8a071bd527]: 201 | Content-Type: application/json; charset=utf-8 | Content-Length: 402 | Duration: 321.896ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************.c_bFEXTV877QrYvGWBztcto7YMpG2CK4GhqhVokiqLU","user":{"id":7,"username":"login","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK4","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.289 +07:00 INF] HTTP Request [5addeff7-dd20-48ba-8832-64011a387b33]: POST /api/Auth/login  | Content-Type: application/json; charset=utf-8 | Content-Length: 76 | Body: {"email":"<EMAIL>","password":"TestPass@word1","rememberMe":false} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK6","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.453 +07:00 INF] Performance [5addeff7-dd20-48ba-8832-64011a387b33]: POST /api/Auth/login completed in 163.4111ms with status 200. {"CorrelationId":"5addeff7-dd20-48ba-8832-64011a387b33","Method":"POST","Path":"/api/Auth/login","QueryString":"","StatusCode":200,"DurationMs":163.4111,"StartTime":"2025-06-17T10:17:55.2898041Z","EndTime":"2025-06-17T10:17:55.4532158Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":76} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AK6","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.453 +07:00 INF] HTTP Response [5addeff7-dd20-48ba-8832-64011a387b33]: 200 | Content-Type: application/json; charset=utf-8 | Content-Length: 402 | Duration: 164.1866ms | Body: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************.c_bFEXTV877QrYvGWBztcto7YMpG2CK4GhqhVokiqLU","user":{"id":7,"username":"login","email":"<EMAIL>","role":"User"}} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK6","RequestPath":"/api/Auth/login","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.457 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:55.457 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
