[2025-06-17 17:17:54.851 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.854 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.856 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.856 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.857 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.881 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.895 +07:00 INF] HTTP Request [0da5f835-0120-4c4d-9577-61d862fafa95]: GET /api/Auth/user  | Content-Type: N/A | Content-Length: 0 | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK2","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.898 +07:00 INF] Cache warmup completed in 43.255ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.898 +07:00 INF] Performance [0da5f835-0120-4c4d-9577-61d862fafa95]: GET /api/Auth/user completed in 2.1594ms with status 401. {"CorrelationId":"0da5f835-0120-4c4d-9577-61d862fafa95","Method":"GET","Path":"/api/Auth/user","QueryString":"","StatusCode":401,"DurationMs":2.1594,"StartTime":"2025-06-17T10:17:54.8960671Z","EndTime":"2025-06-17T10:17:54.8982269Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":0} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDH0SJ5AK2","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.898 +07:00 WRN] HTTP Response [0da5f835-0120-4c4d-9577-61d862fafa95]: 401 | Content-Type: N/A | Content-Length: 0 | Duration: 2.5275ms | Body: N/A {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDH0SJ5AK2","RequestPath":"/api/Auth/user","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.899 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:17:54.899 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
