[2025-06-17 17:15:30.357 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.361 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.362 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.362 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.364 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.366 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.400 +07:00 INF] Cache warmup completed in 38.6097ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.400 +07:00 INF] HTTP Request [ee45873a-60f8-47a2-b14f-fa8e8df2fff3]: POST /api/Auth/register  | Content-Type: application/json; charset=utf-8 | Content-Length: 273 | Body: {"username":"testuser","email":"<EMAIL>","password":"TestPass@word1","confirmPassword":"TestPass@word1","firstName":"Test","lastName":"User","phone":"\u002B1234567890","dateOfBirth":"2000-06-17T17:15:30.3945628+07:00","acceptTerms":true,"acceptPrivacyPolicy":true} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHI9","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.451 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-c92220ea53657708efd37517ab4b2a04-2318f1ba8934f25a-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"c6c52f0f-ea76-4390-809a-940e26a72816","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDDGVHETHI9","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.453 +07:00 INF] Performance [ee45873a-60f8-47a2-b14f-fa8e8df2fff3]: POST /api/Auth/register completed in 52.4634ms with status 400. {"CorrelationId":"ee45873a-60f8-47a2-b14f-fa8e8df2fff3","Method":"POST","Path":"/api/Auth/register","QueryString":"","StatusCode":400,"DurationMs":52.4634,"StartTime":"2025-06-17T10:15:30.4004699Z","EndTime":"2025-06-17T10:15:30.4529334Z","ContentLength":0,"UserAgent":null,"RemoteIpAddress":null,"RequestSize":273} {"SourceContext":"DecorStore.API.Middleware.PerformanceLoggingMiddleware","RequestId":"0HNDDGVHETHI9","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.453 +07:00 WRN] HTTP Response [ee45873a-60f8-47a2-b14f-fa8e8df2fff3]: 400 | Content-Type: application/json; charset=utf-8 | Content-Length: 194 | Duration: 52.9178ms | Body: {"error":"User with this email already exists","errorCode":"USER_ALREADY_EXISTS","correlationId":"00-c92220ea53657708efd37517ab4b2a04-2318f1ba8934f25a-00","timestamp":"2025-06-17T10:15:30.451Z"} {"SourceContext":"DecorStore.API.Middleware.RequestResponseLoggingMiddleware","RequestId":"0HNDDGVHETHI9","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.454 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
[2025-06-17 17:15:30.454 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Development"}
