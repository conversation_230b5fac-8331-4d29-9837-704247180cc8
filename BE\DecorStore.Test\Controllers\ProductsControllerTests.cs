using System.Net;
using System.Net.Http.Json;
using DecorStore.API.DTOs;
using FluentAssertions;
using Xunit;

namespace DecorStore.Test.Controllers
{
    public class ProductsControllerTests : TestBase
    {
        [Fact]
        public async Task GetProducts_ShouldReturnProductList()
        {
            // Arrange
            await SeedTestDataAsync();

            // Act
            var response = await _client.GetAsync("/api/Products");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var products = await DeserializeResponseAsync<List<ProductDTO>>(response);
            products.Should().NotBeNull();
            products!.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task GetProduct_WithValidId_ShouldReturnProduct()
        {
            // Arrange
            await SeedTestDataAsync();
            var allProducts = await _client.GetFromJsonAsync<List<ProductDTO>>("/api/Products");
            var productId = allProducts!.First().Id;

            // Act
            var response = await _client.GetAsync($"/api/Products/{productId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var product = await DeserializeResponseAsync<ProductDTO>(response);
            product.Should().NotBeNull();
            product!.Id.Should().Be(productId);
        }

        [Fact]
        public async Task GetProduct_WithInvalidId_ShouldReturnNotFound()
        {
            // Act
            var response = await _client.GetAsync("/api/Products/99999");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task CreateProduct_WithValidData_ShouldReturnCreated()
        {
            // Arrange
            await SeedTestDataAsync();
            var categories = await _client.GetFromJsonAsync<List<CategoryDTO>>("/api/Category");
            var categoryId = categories!.First().Id;

            var createProductDto = new CreateProductDTO
            {
                Name = "Test Product",
                Description = "A test product description",
                Price = 99.99m,
                CategoryId = categoryId,
                IsActive = true,
                IsFeatured = false,
                StockQuantity = 10
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/Products", createProductDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var product = await DeserializeResponseAsync<ProductDTO>(response);
            product.Should().NotBeNull();
            product!.Name.Should().Be(createProductDto.Name);
            product.Price.Should().Be(createProductDto.Price);
        }

        [Fact]
        public async Task CreateProduct_WithInvalidData_ShouldReturnBadRequest()
        {
            // Arrange
            var createProductDto = new CreateProductDTO
            {
                Name = "", // Invalid: empty name
                Description = "A test product description",
                Price = -10, // Invalid: negative price
                CategoryId = 99999, // Invalid: non-existent category
                IsActive = true,
                IsFeatured = false,
                StockQuantity = -5 // Invalid: negative stock
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/Products", createProductDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task UpdateProduct_WithValidData_ShouldReturnOk()
        {
            // Arrange
            await SeedTestDataAsync();
            var allProducts = await _client.GetFromJsonAsync<List<ProductDTO>>("/api/Products");
            var product = allProducts!.First();

            var updateProductDto = new UpdateProductDTO
            {
                Name = "Updated Product Name",
                Description = "Updated description",
                Price = 199.99m,
                CategoryId = product.CategoryId,
                IsActive = true,
                IsFeatured = true,
                StockQuantity = 20
            };

            // Act
            var response = await _client.PutAsJsonAsync($"/api/Products/{product.Id}", updateProductDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var updatedProduct = await DeserializeResponseAsync<ProductDTO>(response);
            updatedProduct.Should().NotBeNull();
            updatedProduct!.Name.Should().Be(updateProductDto.Name);
            updatedProduct.Price.Should().Be(updateProductDto.Price);
        }

        [Fact]
        public async Task UpdateProduct_WithInvalidId_ShouldReturnNotFound()
        {
            // Arrange
            var updateProductDto = new UpdateProductDTO
            {
                Name = "Updated Product Name",
                Description = "Updated description",
                Price = 199.99m,
                CategoryId = 1,
                IsActive = true,
                IsFeatured = true,
                StockQuantity = 20
            };

            // Act
            var response = await _client.PutAsJsonAsync("/api/Products/99999", updateProductDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task DeleteProduct_WithValidId_ShouldReturnNoContent()
        {
            // Arrange
            await SeedTestDataAsync();
            var allProducts = await _client.GetFromJsonAsync<List<ProductDTO>>("/api/Products");
            var productId = allProducts!.First().Id;

            // Act
            var response = await _client.DeleteAsync($"/api/Products/{productId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NoContent);
            
            // Verify product is deleted
            var getResponse = await _client.GetAsync($"/api/Products/{productId}");
            getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task DeleteProduct_WithInvalidId_ShouldReturnNotFound()
        {
            // Act
            var response = await _client.DeleteAsync("/api/Products/99999");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task GetProductsByCategory_ShouldReturnFilteredProducts()
        {
            // Arrange
            await SeedTestDataAsync();
            var categories = await _client.GetFromJsonAsync<List<CategoryDTO>>("/api/Category");
            var categoryId = categories!.First().Id;

            // Act
            var response = await _client.GetAsync($"/api/Products/category/{categoryId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var products = await DeserializeResponseAsync<List<ProductDTO>>(response);
            products.Should().NotBeNull();
            products!.Should().OnlyContain(p => p.CategoryId == categoryId);
        }

        [Fact]
        public async Task SearchProducts_ShouldReturnMatchingProducts()
        {
            // Arrange
            await SeedTestDataAsync();

            // Act
            var response = await _client.GetAsync("/api/Products/search?query=Test");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var products = await DeserializeResponseAsync<List<ProductDTO>>(response);
            products.Should().NotBeNull();
        }

        [Fact]
        public async Task GetFeaturedProducts_ShouldReturnOnlyFeaturedProducts()
        {
            // Arrange
            await SeedTestDataAsync();

            // Act
            var response = await _client.GetAsync("/api/Products/featured");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var products = await DeserializeResponseAsync<List<ProductDTO>>(response);
            products.Should().NotBeNull();
            products!.Should().OnlyContain(p => p.IsFeatured);
        }
    }
}
